import { MemoryClient } from 'mem0ai';
import { memoryConfig } from './memoryConfig';
import { setupMemoryMock } from '../scripts/mock-memory';
import fs from 'node:fs';
import path from 'node:path';
import zlib from 'node:zlib'; // Importation de la bibliothèque pour la compression

// Déterminer si nous sommes en environnement de test
const isPlaywrightTest = process.env.PLAYWRIGHT === 'True';

// Configurations
const BATCH_INTERVAL_MS = 500; // Réduit de 3000ms à 500ms
const MAX_BATCH_SIZE = 10;
const MAX_ATTEMPTS = 3;
const BACKOFF_BASE = 1000;
const BACKOFF_MAX = 10000;
const MAX_QUEUE_SIZE = 1000;
const QUEUE_PERSISTENCE_FILE = path.join(__dirname, 'memoryQueue.json');
const ENABLE_PERSISTENCE = process.env.MEMORY_PERSIST === 'true';
const DEBUG = process.env.MEMORY_DEBUG === 'true';

// Initialisation client
const client = isPlaywrightTest
  ? setupMemoryMock()
  : memoryConfig.isValid()
    ? new MemoryClient({ apiKey: memoryConfig.apiKey })
    : null;

let flushTimer: NodeJS.Timeout | null = null;
let currentInterval = BATCH_INTERVAL_MS;
let isFlushing = false;

const queue: {
  data: string;
  userId: string;
  metadata: Record<string, any>;
}[] = loadQueueFromDisk();

// Stocker les labels actifs
const activeTimers = new Set<string>();

// Fonction pour démarrer un timer
function startTimer(label: string) {
  activeTimers.add(label);
  console.time(label);
}

// Fonction pour terminer un timer
function endTimer(label: string) {
  if (activeTimers.has(label)) {
    console.timeEnd(label);
    activeTimers.delete(label);
  }
}

// Fonction de débogage
function logDebug(...args: any[]) {
  if (DEBUG) console.log('[MEMORY DEBUG]', ...args);
}

// Charger la queue depuis le disque (persistance optionnelle)
function loadQueueFromDisk() {
  if (ENABLE_PERSISTENCE && fs.existsSync(QUEUE_PERSISTENCE_FILE)) {
    try {
      const data = fs.readFileSync(QUEUE_PERSISTENCE_FILE, 'utf8');
      return JSON.parse(data);
    } catch (err) {
      console.error('❌ Failed to load memory queue from disk:', err);
    }
  }
  return [];
}

// Sauvegarder la queue sur le disque
function saveQueueToDisk() {
  if (ENABLE_PERSISTENCE) {
    try {
      fs.writeFileSync(QUEUE_PERSISTENCE_FILE, JSON.stringify(queue, null, 2));
      logDebug('Queue saved to disk');
    } catch (err) {
      console.error('❌ Failed to save memory queue to disk:', err);
    }
  }
}

// Fonction pour compresser les données avant de les envoyer
function compressData(data: string): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    // Utiliser un niveau de compression plus faible pour plus de rapidité
    zlib.gzip(data, { level: 1 }, (err, compressedData) => {
      if (err) reject(err);
      resolve(compressedData);
    });
  });
}

// Fonction pour envoyer à Mem0
async function sendToMemory(
  data: string,
  userId: string,
  metadata: Record<string, any>,
  categories?: string[],
): Promise<boolean> {
  if (!client || !memoryConfig.isValid()) return false;

  // Environnement de test, retourner immédiatement true
  if (isPlaywrightTest) return true;

  try {
    // Compression des données avant l'envoi
    const compressedData = await compressData(data);

    // Timeout pour éviter les blocages
    const timeoutPromise = new Promise<boolean>((_, reject) =>
      setTimeout(() => reject(new Error('Memory send timeout')), 5000),
    );

    const sendPromise = client
      .add(compressedData.toString('base64'), {
        // Envoi des données compressées en base64
        user_id: userId,
        org_id: memoryConfig.orgId,
        project_id: memoryConfig.projectId,
        categories: categories, // Ajouter les catégories si elles sont fournies
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString(),
        },
      })
      .then(() => true);

    return await Promise.race([sendPromise, timeoutPromise]);
  } catch (err) {
    console.error('❌ Memory send failed or timed out:', err);
    return false;
  }
}

// Fonction pour vider la queue
async function flushQueue() {
  if (queue.length === 0 || !client || !memoryConfig.isValid() || isFlushing)
    return;

  isFlushing = true;
  const batch = queue.splice(0, MAX_BATCH_SIZE);
  const batchId = new Date().toISOString();
  const timestamp = new Date().toISOString();

  const timerLabel = `memory_batch_${batchId}`;
  console.time(timerLabel);

  try {
    for (let attempt = 1; attempt <= MAX_ATTEMPTS; attempt++) {
      try {
        await Promise.all(
          batch.map(({ data, userId, metadata }) => {
            // Extraire les catégories des métadonnées si elles existent
            let categories: string[] | undefined;

            // Créer une copie des métadonnées sans la propriété _categories
            const { _categories, ...metadataWithoutCategories } = metadata;
            const metadataCopy = { ...metadataWithoutCategories, timestamp };

            if (_categories) {
              categories = _categories;
            }

            return sendToMemory(data, userId, metadataCopy, categories);
          }),
        );

        console.timeEnd(timerLabel);
        console.log(
          `✅ Flushed ${batch.length} memory entries (attempt ${attempt})`,
        );
        return;
      } catch (err) {
        console.error(
          `❌ Failed to flush memory batch (attempt ${attempt}/${MAX_ATTEMPTS}):`,
          err,
        );

        if (attempt < MAX_ATTEMPTS) {
          const delay = Math.min(BACKOFF_BASE * 2 ** attempt, BACKOFF_MAX);
          logDebug(`Retrying in ${delay}ms...`);
          await new Promise((resolve) => setTimeout(resolve, delay));
        } else {
          console.error('All retry attempts failed, requeuing batch');
          queue.unshift(...batch);
          saveQueueToDisk();
        }
      }
    }
  } finally {
    isFlushing = false;
    // Remove the duplicate console.timeEnd call here
  }
}

// Planification du flush
function scheduleFlush() {
  if (!flushTimer) {
    flushTimer = setTimeout(() => {
      flushTimer = null;
      flushQueue();

      if (queue.length > 0) {
        currentInterval = Math.max(500, currentInterval / 2);
        scheduleFlush();
      } else {
        currentInterval = BATCH_INTERVAL_MS;
      }
    }, currentInterval);
  }
}

// Fonction pour ajouter un élément dans la mémoire
export function enqueueMemory({
  data,
  userId,
  metadata,
  categories,
  priority = false,
}: {
  data: string;
  userId: string;
  metadata: Record<string, any>;
  categories?: string[];
  priority?: boolean;
}) {
  if (!data || !userId) {
    console.error('Invalid memory entry: missing data or userId');
    return;
  }

  if (!memoryConfig.isValid() || !client) {
    console.error('Memory client not properly configured.');
    return;
  }

  if (typeof metadata !== 'object' || metadata === null) {
    console.error('Invalid metadata format');
    return;
  }

  try {
    metadata = JSON.parse(JSON.stringify(metadata));
  } catch (err) {
    console.error('Metadata serialization failed:', err);
    return;
  }

  // Envoi immédiat pour les éléments prioritaires
  if (priority) {
    sendToMemory(data, userId, metadata, categories)
      .then((success) => {
        if (!success) {
          // En cas d'échec, ajouter à la queue normale
          addToQueue(data, userId, metadata, categories);
        }
      })
      .catch(() => {
        addToQueue(data, userId, metadata, categories);
      });
    return;
  }

  addToQueue(data, userId, metadata, categories);
}

// Fonction auxiliaire pour ajouter à la queue
function addToQueue(
  data: string,
  userId: string,
  metadata: Record<string, any>,
  categories?: string[],
) {
  if (queue.length >= MAX_QUEUE_SIZE) {
    console.warn('⚠️ Queue is full, dropping oldest entry');
    queue.shift();
  }

  // Ajouter les catégories aux métadonnées pour les stocker dans la queue
  if (categories && categories.length > 0) {
    metadata._categories = categories;
  }

  queue.push({ data, userId, metadata });
  saveQueueToDisk();

  if (queue.length >= MAX_BATCH_SIZE) {
    if (!isFlushing) flushQueue();
    if (flushTimer) {
      clearTimeout(flushTimer);
      flushTimer = null;
    }
  } else {
    scheduleFlush();
  }
}

// Fonction pour ajouter une entrée combinée dans la mémoire
export function enqueueCombinedMemory({
  entries,
  userId,
}: {
  entries: { data: string; action: string; metadata?: Record<string, any> }[];
  userId: string;
}) {
  if (!entries.length || !userId || !memoryConfig.isValid()) {
    console.error('Invalid combined memory entry');
    return;
  }

  const combinedData = entries.map((entry) => entry.data).join('\n\n---\n\n');

  const combinedMetadata: Record<string, any> = {
    actions: entries.map((entry) => entry.action),
    timestamp: new Date().toISOString(),
  };

  entries.forEach((entry, index) => {
    if (entry.metadata && typeof entry.metadata === 'object') {
      Object.entries(entry.metadata).forEach(([key, value]) => {
        combinedMetadata[`${key}_${index}`] = value;
      });
    }
  });

  enqueueMemory({
    data: combinedData,
    userId,
    metadata: combinedMetadata,
  });
}

// Obtenir l'état de la queue
export function getQueueStatus() {
  return {
    length: queue.length,
    isFlushing,
    flushTimerActive: !!flushTimer,
  };
}
