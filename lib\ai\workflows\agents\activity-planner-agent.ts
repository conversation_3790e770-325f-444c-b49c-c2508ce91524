import { z } from 'zod';
import { generateObject, type LanguageModelV1 } from 'ai';
import type { DestinationInfo, DayItinerary } from '../types';
import type { UserPreferences } from './preference-agent';
import type { LocalRecommendations } from './local-expert-agent';
import { web_search } from '@/lib/ai/tools/web-search';
import type { DataStreamWriter } from 'ai';
import {
  withAIModelResilience,
  createMockGenerateObjectResult,
} from '../api-resilience';

/**
 * Interface for activity options
 */
export interface ActivityOption {
  name: string;
  description: string;
  duration: string;
  cost: string;
  bestFor: string[];
  accessibility: string;
  bookingInfo?: string;
  coordinates: {
    lat: string;
    lng: string;
  };
}

/**
 * Interface for activity plan
 */
export interface ActivityPlan {
  days: DayItinerary[];
  alternativeActivities: {
    day: number;
    options: ActivityOption[];
  }[];
  rainyDayOptions: ActivityOption[];
  eveningEntertainment: ActivityOption[];
  familyFriendlyOptions?: ActivityOption[];
  accessibleOptions?: ActivityOption[];
  sustainableOptions?: ActivityOption[];
}

/**
 * ActivityPlannerAgent is responsible for creating a personalized activity plan
 * based on user preferences and local recommendations.
 */
export class ActivityPlannerAgent {
  private model: LanguageModelV1;

  constructor(model: LanguageModelV1) {
    this.model = model;
  }

  /**
   * Create a personalized activity plan
   */
  async createActivityPlan(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
    localRecommendations: LocalRecommendations,
  ): Promise<ActivityPlan> {
    try {
      console.log('Creating activity plan for:', destinationInfo.destination);

      // Check if duration is null or undefined
      if (
        destinationInfo.duration === null ||
        destinationInfo.duration === undefined
      ) {
        console.error(
          'Duration is null or undefined. Cannot create activity plan.',
        );
        return this.getFallbackActivityPlan(destinationInfo);
      }

      // Execute all tool searches in parallel for better performance
      const [searchResults, locationResults] = await Promise.all([
        this.searchActivities(destinationInfo, userPreferences),
        this.searchActivityLocations(destinationInfo, userPreferences),
      ]);

      // Determine if this is a long trip (7+ days) for enhanced processing
      const isLongTrip = destinationInfo.duration >= 7;
      console.log(
        `Planning ${isLongTrip ? 'LONG' : 'SHORT'} trip for ${destinationInfo.duration} days`,
      );

      // Generate activity plan using the model with enhanced error handling
      const generateObjectResult = await withAIModelResilience(
        () =>
          generateObject({
            model: this.model,
            system: `You are an expert activity planner who specializes in creating comprehensive, detailed itineraries.
          Create a full day-by-day activity plan with 8-12 activities per day, including:
          - Early morning activities (7:00-9:00 AM)
          - Morning activities and sightseeing (9:00-12:00 PM)
          - Lunch at specific restaurants with names and locations
          - Afternoon activities and cultural visits (2:00-5:00 PM)
          - Evening activities and entertainment (5:00-8:00 PM)
          - Dinner at specific restaurants with cuisine types
          - Night activities, bars, or evening strolls (8:00-11:00 PM)

          ${
            isLongTrip
              ? `
          SPECIAL INSTRUCTIONS FOR LONG TRIPS (7+ days):
          - Provide SPECIFIC restaurant names, attraction names, and exact addresses
          - Include famous landmarks, museums, restaurants, and local experiences
          - Vary activities across different neighborhoods and districts
          - Include both must-see attractions and hidden local gems
          - Ensure each day has a different theme or focus area
          - Add specific opening hours and booking requirements where relevant
          `
              : ''
          }

          Include specific restaurant names, exact locations, activity durations, and detailed descriptions.
          Consider realistic travel times between locations and provide comprehensive daily experiences.
          Each day should feel complete from morning to night with varied activities and dining options.`,
            prompt: `Create a personalized activity plan for ${destinationInfo.destination}, ${destinationInfo.country}
        for ${destinationInfo.duration} days based on these user preferences:

        ${JSON.stringify(userPreferences, null, 2)}

        Consider these local recommendations:
        ${JSON.stringify(localRecommendations, null, 2)}

        And these activity search results:
        Web Search Results: ${JSON.stringify(searchResults, null, 2)}
        Location Results: ${JSON.stringify(locationResults, null, 2)}

        Create a comprehensive day-by-day plan with 8-12 activities per day covering the entire day:

        FOR EACH DAY INCLUDE:

        MORNING (7:00-12:00):
        - Specific breakfast restaurant with name and exact location
        - 2-3 morning activities (sightseeing, cultural visits, markets, temples)

        AFTERNOON (12:00-17:00):
        - Specific lunch restaurant with cuisine type and exact location
        - 2-3 afternoon activities (museums, parks, shopping districts, tours)

        EVENING/NIGHT (17:00-23:00):
        - Specific dinner restaurant with cuisine type and exact location
        - 2-3 evening/night activities (entertainment, bars, night markets, scenic walks, shows)

        CRITICAL REQUIREMENTS - MUST BE FOLLOWED:
        - Use REAL, SPECIFIC restaurant names (e.g., "Le Comptoir du Relais" not "local restaurant")
        - Use REAL, SPECIFIC attraction names (e.g., "Louvre Museum" not "main museum")
        - Use REAL, SPECIFIC addresses and neighborhoods (e.g., "1 Rue de Rivoli, 75001 Paris")
        - NEVER use generic terms like "local", "traditional", "main", "central", "popular", "famous"
        - Research and include actual business names that exist in the real world
        - Provide exact addresses and neighborhoods when possible
        - Include realistic activity durations and travel times
        - Mix cultural, culinary, entertainment, shopping, and relaxation activities
        - Include both popular attractions and hidden local gems
        - Consider local specialties and must-try experiences
        - Make each day feel complete from morning to night

        EXAMPLES OF GOOD vs BAD:
        ✅ GOOD: "Breakfast at Café de Flore, 172 Boulevard Saint-Germain, 75006 Paris"
        ❌ BAD: "Breakfast at local café in city center"
        ✅ GOOD: "Visit Louvre Museum, Rue de Rivoli, 75001 Paris"
        ❌ BAD: "Visit main museum in downtown area"

        ${userPreferences.travelWithChildren ? 'Include family-friendly options suitable for children.' : ''}
        ${userPreferences.accessibility.mobilityIssues ? 'Prioritize accessible options and consider mobility issues.' : ''}
        ${userPreferences.sustainabilityPreference ? 'Include sustainable and eco-friendly activity options.' : ''}`,
            schema: z.object({
              days: z
                .array(
                  z.object({
                    day: z.number().describe('Day number'),
                    activities: z
                      .array(
                        z.object({
                          time: z
                            .string()
                            .describe('Time of the activity (e.g., "09:00")'),
                          activity: z
                            .string()
                            .describe(
                              'Specific name of the activity or restaurant',
                            ),
                          location: z
                            .string()
                            .describe(
                              'Exact location with address or neighborhood',
                            ),
                          description: z
                            .string()
                            .describe('Detailed description of the activity'),
                          poiIndex: z
                            .number()
                            .optional()
                            .describe('Reference to the POI in the POIs array'),
                        }),
                      )
                      .min(8)
                      .max(12)
                      .describe(
                        '8-12 activities covering the full day from morning to night',
                      ),
                  }),
                )
                .describe('Day-by-day activity plan'),

              alternativeActivities: z
                .array(
                  z.object({
                    day: z.number().describe('Day number'),
                    options: z.array(
                      z.object({
                        name: z.string().describe('Name of the activity'),
                        description: z
                          .string()
                          .describe('Description of the activity'),
                        duration: z.string().describe('Estimated duration'),
                        cost: z.string().describe('Estimated cost'),
                        bestFor: z
                          .array(z.string())
                          .describe('What this activity is best for'),
                        accessibility: z
                          .string()
                          .describe('Accessibility information'),
                        bookingInfo: z
                          .string()
                          .optional()
                          .describe('Booking information if needed'),
                        coordinates: z.object({
                          lat: z.string().describe('Latitude coordinate'),
                          lng: z.string().describe('Longitude coordinate'),
                        }),
                      }),
                    ),
                  }),
                )
                .describe('Alternative activity options for each day'),

              rainyDayOptions: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the activity'),
                    description: z
                      .string()
                      .describe('Description of the activity'),
                    duration: z.string().describe('Estimated duration'),
                    cost: z.string().describe('Estimated cost'),
                    bestFor: z
                      .array(z.string())
                      .describe('What this activity is best for'),
                    accessibility: z
                      .string()
                      .describe('Accessibility information'),
                    bookingInfo: z
                      .string()
                      .optional()
                      .describe('Booking information if needed'),
                    coordinates: z.object({
                      lat: z.string().describe('Latitude coordinate'),
                      lng: z.string().describe('Longitude coordinate'),
                    }),
                  }),
                )
                .describe('Activity options for rainy days'),

              eveningEntertainment: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the activity'),
                    description: z
                      .string()
                      .describe('Description of the activity'),
                    duration: z.string().describe('Estimated duration'),
                    cost: z.string().describe('Estimated cost'),
                    bestFor: z
                      .array(z.string())
                      .describe('What this activity is best for'),
                    accessibility: z
                      .string()
                      .describe('Accessibility information'),
                    bookingInfo: z
                      .string()
                      .optional()
                      .describe('Booking information if needed'),
                    coordinates: z.object({
                      lat: z.string().describe('Latitude coordinate'),
                      lng: z.string().describe('Longitude coordinate'),
                    }),
                  }),
                )
                .describe('Evening entertainment options'),

              familyFriendlyOptions: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the activity'),
                    description: z
                      .string()
                      .describe('Description of the activity'),
                    duration: z.string().describe('Estimated duration'),
                    cost: z.string().describe('Estimated cost'),
                    bestFor: z
                      .array(z.string())
                      .describe('What this activity is best for'),
                    accessibility: z
                      .string()
                      .describe('Accessibility information'),
                    bookingInfo: z
                      .string()
                      .optional()
                      .describe('Booking information if needed'),
                    coordinates: z.object({
                      lat: z.string().describe('Latitude coordinate'),
                      lng: z.string().describe('Longitude coordinate'),
                    }),
                  }),
                )
                .optional()
                .describe('Family-friendly activity options'),

              accessibleOptions: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the activity'),
                    description: z
                      .string()
                      .describe('Description of the activity'),
                    duration: z.string().describe('Estimated duration'),
                    cost: z.string().describe('Estimated cost'),
                    bestFor: z
                      .array(z.string())
                      .describe('What this activity is best for'),
                    accessibility: z
                      .string()
                      .describe('Accessibility information'),
                    bookingInfo: z
                      .string()
                      .optional()
                      .describe('Booking information if needed'),
                    coordinates: z.object({
                      lat: z.string().describe('Latitude coordinate'),
                      lng: z.string().describe('Longitude coordinate'),
                    }),
                  }),
                )
                .optional()
                .describe('Accessible activity options'),

              sustainableOptions: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the activity'),
                    description: z
                      .string()
                      .describe('Description of the activity'),
                    duration: z.string().describe('Estimated duration'),
                    cost: z.string().describe('Estimated cost'),
                    bestFor: z
                      .array(z.string())
                      .describe('What this activity is best for'),
                    accessibility: z
                      .string()
                      .describe('Accessibility information'),
                    bookingInfo: z
                      .string()
                      .optional()
                      .describe('Booking information if needed'),
                    coordinates: z.object({
                      lat: z.string().describe('Latitude coordinate'),
                      lng: z.string().describe('Longitude coordinate'),
                    }),
                  }),
                )
                .optional()
                .describe('Sustainable and eco-friendly activity options'),
            }),
            temperature: 0.7,
          }),
        'gemini-2.0-flash',
        isLongTrip
          ? 'long-trip-activity-plan-generation'
          : 'activity-plan-generation',
        () =>
          Promise.resolve(
            createMockGenerateObjectResult(
              this.getFallbackActivityPlan(destinationInfo),
            ),
          ),
      );

      // Log the generated activity plan with detailed analysis
      console.log(
        '🎯 Generated activity plan:',
        JSON.stringify(generateObjectResult.object, null, 2),
      );

      // Check if this is using fallback data
      const activityPlan = generateObjectResult.object;
      const firstActivity = activityPlan.days[0]?.activities[0];
      const isUsingFallback = firstActivity && (
        firstActivity.activity.includes('Bills Omotesando') ||
        firstActivity.activity.includes('Café de Flore') ||
        firstActivity.location.includes('City Center')
      );

      if (isUsingFallback) {
        console.log('🚨 USING FALLBACK PLAN - AI generation likely failed or timed out');
        console.log('🔄 This explains why all days are identical');
      } else {
        console.log('✅ Using AI-generated plan');
      }

      // Analyze the specificity of generated activities
      console.log('📊 Activity Plan Analysis:');
      console.log(`- Total days: ${activityPlan.days.length}`);

      activityPlan.days.forEach((day: any, index: number) => {
        console.log(`📅 Day ${day.day} (${day.activities.length} activities):`);
        day.activities.slice(0, 3).forEach((activity: any, actIndex: number) => {
          console.log(
            `  ${actIndex + 1}. ${activity.time} - ${activity.activity} at ${activity.location}`,
          );
        });
        if (day.activities.length > 3) {
          console.log(`  ... and ${day.activities.length - 3} more activities`);
        }
      });

      return generateObjectResult.object;
    } catch (error) {
      console.error('Error creating activity plan:', error);

      // Return fallback activity plan if generation fails
      return this.getFallbackActivityPlan(destinationInfo);
    }
  }

  /**
   * Search for activities in the destination
   */
  private async searchActivities(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
  ): Promise<any> {
    try {
      // Create a mock session and dataStream for the web_search tool
      const mockSession = {
        user: { id: 'system', type: 'guest' as const },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };
      // Create a mock DataStreamWriter
      const mockDataStream: DataStreamWriter = {
        writeMessageAnnotation: () => {},
        write: () => '',
        writeData: () => '',
        writeSource: () => '',
        merge: () => '',
        onError: () => '',
      };

      // Use the web_search tool to get activity information
      const searchTool = web_search({
        session: mockSession,
        dataStream: mockDataStream,
      });

      // Create search queries based on user preferences
      const queries = [
        `top attractions in ${destinationInfo.destination} ${destinationInfo.country}`,
        `${userPreferences.activities.activityLevel} activities in ${destinationInfo.destination}`,
      ];

      // Add preference-specific queries
      if (userPreferences.travelWithChildren) {
        queries.push(
          `family-friendly activities in ${destinationInfo.destination}`,
        );
      }

      if (userPreferences.accessibility.mobilityIssues) {
        queries.push(
          `accessible activities in ${destinationInfo.destination} for mobility issues`,
        );
      }

      if (userPreferences.sustainabilityPreference) {
        queries.push(
          `sustainable eco-friendly activities in ${destinationInfo.destination}`,
        );
      }

      // Add activity interest queries
      for (const interest of userPreferences.activities.interests.slice(0, 2)) {
        queries.push(
          `${interest} in ${destinationInfo.destination} ${destinationInfo.country}`,
        );
      }

      // Execute all search queries in parallel
      const searchPromises = queries.map((query) =>
        (searchTool.execute as any)({
          queries: [query],
          maxResults: [3],
          topics: ['general'],
        }),
      );

      const results = await Promise.all(searchPromises);
      return results;
    } catch (error) {
      console.error('Error searching activities:', error);
      return { results: [] };
    }
  }

  /**
   * Get fallback activity plan if generation fails
   */
  private getFallbackActivityPlan(
    destinationInfo: DestinationInfo,
  ): ActivityPlan {
    const days: DayItinerary[] = [];

    // Use a default duration of 3 days if duration is null
    const actualDuration =
      destinationInfo.duration !== null ? destinationInfo.duration : 3;
    console.log(
      `Using ENHANCED fallback duration of ${actualDuration} days for activity plan generation`,
    );

    // Create a basic activity plan for each day with destination-specific names
    for (let i = 1; i <= actualDuration; i++) {
      const dayActivities = this.generateDestinationSpecificFallbackActivities(
        destinationInfo,
        i,
      );
      days.push({
        day: i,
        activities: dayActivities,
      });
    }

    return {
      days,
      alternativeActivities: [
        {
          day: 1,
          options: [
            {
              name: 'Park Visit',
              description: 'Visit a local park for relaxation.',
              duration: '2 hours',
              cost: 'Free',
              bestFor: ['Relaxation', 'Nature lovers'],
              accessibility: 'Generally accessible',
              coordinates: {
                lat: destinationInfo.coordinates.lat,
                lng: destinationInfo.coordinates.lng,
              },
            },
          ],
        },
      ],
      rainyDayOptions: [
        {
          name: 'Museum Visit',
          description: 'Visit a local museum to stay dry.',
          duration: '3 hours',
          cost: 'Moderate',
          bestFor: ['Rainy days', 'Culture enthusiasts'],
          accessibility: 'Generally accessible',
          coordinates: {
            lat: destinationInfo.coordinates.lat,
            lng: destinationInfo.coordinates.lng,
          },
        },
      ],
      eveningEntertainment: [
        {
          name: 'Local Performance',
          description: 'Enjoy a local music or theater performance.',
          duration: '2 hours',
          cost: 'Moderate',
          bestFor: ['Evening entertainment', 'Cultural experience'],
          accessibility: 'Generally accessible',
          coordinates: {
            lat: destinationInfo.coordinates.lat,
            lng: destinationInfo.coordinates.lng,
          },
        },
      ],
    };
  }

  /**
   * Generate destination-specific fallback activities for a day
   */
  private generateDestinationSpecificFallbackActivities(
    destinationInfo: DestinationInfo,
    dayNumber: number,
  ): Array<{
    time: string;
    activity: string;
    location: string;
    description: string;
  }> {
    const { destination, country } = destinationInfo;

    // Create more specific activities based on common destination patterns
    const getSpecificRestaurantName = (type: string, dayIndex: number = 0) => {
      const restaurantNames = {
        Paris: {
          breakfast: ['Café de Flore', 'Du Pain et des Idées', 'Breizh Café', 'L\'As du Fallafel', 'Pierre Hermé', 'Angelina', 'Café Procope', 'Lomi'],
          lunch: ['Le Comptoir du Relais', 'L\'Ami Jean', 'Bistrot Paul Bert', 'Le Chateaubriand', 'Septime', 'Le Mary Celeste', 'Du Pain et des Idées', 'Marché Saint-Germain'],
          dinner: ['Le Grand Véfour', 'L\'Ambroisie', 'Guy Savoy', 'Le Bristol', 'Epicure', 'Le Meurice', 'Pierre Gagnaire', 'Alain Ducasse'],
        },
        Tokyo: {
          breakfast: ['Bills Omotesando', 'Tsukiji Outer Market', 'Café Aaliya', 'Fluffy Pancakes', 'Sarabeth\'s Tokyo', 'Blue Seal', 'Gram Café', 'Ivy Place'],
          lunch: ['Sukiyabashi Jiro', 'Ramen Yashichi', 'Tonki', 'Daiwa Sushi', 'Kozasa', 'Menya Saimi', 'Ichiran Ramen', 'Sushi Dai'],
          dinner: ['Narisawa', 'Den', 'Florilège', 'Nihonryori RyuGin', 'Quintessence', 'L\'Effervescence', 'Kanda', 'Kikunoi'],
        },
        London: {
          breakfast: ['The Breakfast Club', 'Dishoom', 'Duck & Waffle', 'Sketch', 'The Ivy', 'Fortnum & Mason', 'Granger & Co', 'Eggbreak'],
          lunch: ['Dishoom', 'Padella', 'Borough Market', 'Hawksmoor', 'Rules', 'Simpson\'s in the Strand', 'Barrafina', 'St. John'],
          dinner: ['Sketch', 'The Ledbury', 'Core by Clare Smyth', 'Alain Ducasse', 'Dinner by Heston', 'Restaurant Gordon Ramsay', 'Fera', 'Ikoyi'],
        },
        'New York': {
          breakfast: ['Sarabeth\'s', 'Clinton St. Baking', 'Balthazar', 'Russ & Daughters', 'Jacob\'s Pickles', 'Bubby\'s', 'Jack\'s Wife Freda', 'The Smith'],
          lunch: ['Katz\'s Delicatessen', 'Joe\'s Pizza', 'Xi\'an Famous Foods', 'The Halal Guys', 'Shake Shack', 'Peter Luger', 'Russ & Daughters', 'Eataly'],
          dinner: ['Le Bernardin', 'Eleven Madison Park', 'Per Se', 'Daniel', 'Jean-Georges', 'Gramercy Tavern', 'The Modern', 'Blue Hill'],
        },
        Rome: {
          breakfast: ['Caffè Sant\'Eustachio', 'Tazza d\'Oro', 'Ginger', 'Checchino dal 1887', 'Piperno', 'Caffè Greco', 'Rosati', 'Giolitti'],
          lunch: ['Da Enzo al 29', 'Trattoria Monti', 'Checchino dal 1887', 'Il Sorpasso', 'Armando al Pantheon', 'Da Valentino', 'Piperno', 'Flavio al Velavevodetto'],
          dinner: ['La Pergola', 'Glass Hostaria', 'Il Pagliaccio', 'Metamorfosi', 'All\'Oro', 'Piperno', 'Checchino dal 1887', 'Da Enzo al 29'],
        },
        Barcelona: {
          breakfast: ['Café Central', 'Els Quatre Gats', 'Granja M. Viader', 'Satan\'s Coffee Corner', 'Nomad Coffee', 'Federal Café', 'Milk Bar & Bistro', 'Brunch & Cake'],
          lunch: ['Cal Pep', 'Bar Mut', 'Quimet & Quimet', 'La Boqueria', 'Can Culleretes', 'Els Quatre Gats', 'Bar del Pla', 'Tapas 24'],
          dinner: ['Disfrutar', 'ABaC', 'Lasarte', 'Moments', 'Cinc Sentits', 'Alkimia', 'Hoja Santa', 'Enigma'],
        },
      };

      const cityRestaurants =
        restaurantNames[destination as keyof typeof restaurantNames];
      if (cityRestaurants) {
        const typeRestaurants = cityRestaurants[type as keyof typeof cityRestaurants];
        if (typeRestaurants && typeRestaurants.length > 0) {
          return typeRestaurants[dayIndex % typeRestaurants.length];
        }
      }

      // Generic fallback
      return type === 'breakfast'
        ? `Local Breakfast Café in ${destination}`
        : type === 'lunch'
          ? `Traditional Restaurant in ${destination}`
          : `Fine Dining Restaurant in ${destination}`;
    };

      const cityRestaurants =
        restaurantNames[destination as keyof typeof restaurantNames];
      if (cityRestaurants) {
        return cityRestaurants[type as keyof typeof cityRestaurants];
      }

      // Generic fallback
      return type === 'breakfast'
        ? `Local Breakfast Café in ${destination}`
        : type === 'lunch'
          ? `Traditional Restaurant in ${destination}`
          : `Fine Dining Restaurant in ${destination}`;
    };

    const getSpecificAttraction = (type: string, dayIndex: number = 0) => {
      const attractions = {
        Paris: {
          museum: ['Louvre Museum', 'Musée d\'Orsay', 'Centre Pompidou', 'Musée Rodin', 'Musée Picasso', 'Musée Carnavalet', 'Musée de l\'Orangerie', 'Palais de Tokyo'],
          landmark: ['Eiffel Tower', 'Arc de Triomphe', 'Notre-Dame Cathedral', 'Sacré-Cœur', 'Panthéon', 'Sainte-Chapelle', 'Place de la Concorde', 'Champs-Élysées'],
          district: ['Montmartre', 'Le Marais', 'Saint-Germain-des-Prés', 'Latin Quarter', 'Belleville', 'Pigalle', 'Bastille', 'Trocadéro'],
        },
        Tokyo: {
          museum: ['Tokyo National Museum', 'Mori Art Museum', 'teamLab Borderless', 'Edo-Tokyo Museum', 'National Museum of Modern Art', 'Nezu Museum', 'Tokyo Metropolitan Museum', 'Suntory Museum'],
          landmark: ['Senso-ji Temple', 'Meiji Shrine', 'Tokyo Skytree', 'Tokyo Tower', 'Imperial Palace', 'Tsukiji Fish Market', 'Rainbow Bridge', 'Yasukuni Shrine'],
          district: ['Shibuya', 'Harajuku', 'Ginza', 'Shinjuku', 'Akihabara', 'Roppongi', 'Asakusa', 'Ueno'],
        },
        London: {
          museum: ['British Museum', 'Tate Modern', 'National Gallery', 'Victoria and Albert Museum', 'Natural History Museum', 'Science Museum', 'Tate Britain', 'Wallace Collection'],
          landmark: ['Tower Bridge', 'Big Ben', 'London Eye', 'Tower of London', 'Buckingham Palace', 'Westminster Abbey', 'St. Paul\'s Cathedral', 'Houses of Parliament'],
          district: ['Covent Garden', 'Camden', 'Notting Hill', 'Shoreditch', 'Greenwich', 'Kensington', 'Soho', 'Borough'],
        },
        'New York': {
          museum: ['Metropolitan Museum', 'MoMA', 'Guggenheim Museum', 'Brooklyn Museum', 'Whitney Museum', 'Frick Collection', 'Museum of Natural History', 'Tenement Museum'],
          landmark: ['Statue of Liberty', 'Empire State Building', 'Brooklyn Bridge', 'Central Park', 'One World Trade Center', 'High Line', 'Rockefeller Center', 'Chrysler Building'],
          district: ['Times Square', 'SoHo', 'Greenwich Village', 'Williamsburg', 'Chelsea', 'Upper East Side', 'Lower East Side', 'Tribeca'],
        },
        Rome: {
          museum: ['Vatican Museums', 'Capitoline Museums', 'Palazzo Altemps', 'Palazzo Massimo', 'Baths of Diocletian', 'Crypta Balbi', 'Palazzo Nuovo', 'Centrale Montemartini'],
          landmark: ['Colosseum', 'Roman Forum', 'Pantheon', 'Trevi Fountain', 'Spanish Steps', 'Castel Sant\'Angelo', 'Palatine Hill', 'Baths of Caracalla'],
          district: ['Trastevere', 'Campo de\' Fiori', 'Testaccio', 'Monti', 'Prati', 'Borgo', 'Esquilino', 'Aventine'],
        },
        Barcelona: {
          museum: ['Picasso Museum', 'Joan Miró Foundation', 'MNAC', 'MACBA', 'CosmoCaixa', 'Museu Frederic Marès', 'Casa Batlló', 'Casa Milà'],
          landmark: ['Sagrada Familia', 'Park Güell', 'Casa Batlló', 'La Rambla', 'Gothic Cathedral', 'Palau Sant Jordi', 'Arc de Triomf', 'Montjuïc'],
          district: ['Gothic Quarter', 'El Born', 'Gràcia', 'Eixample', 'Barceloneta', 'Poble Sec', 'Sant Antoni', 'Poblenou'],
        },
      };

      const cityAttractions =
        attractions[destination as keyof typeof attractions];
      if (cityAttractions) {
        const typeAttractions = cityAttractions[type as keyof typeof cityAttractions];
        if (typeAttractions && typeAttractions.length > 0) {
          return typeAttractions[dayIndex % typeAttractions.length];
        }
      }

      // Generic fallback
      return type === 'museum'
        ? `${destination} City Museum`
        : type === 'landmark'
          ? `${destination} Main Landmark`
          : `${destination} Historic District`;
    };

    // Use dayNumber-1 as index to get different restaurants/attractions for each day
    const dayIndex = dayNumber - 1;

    return [
      {
        time: '07:30',
        activity: `Breakfast at ${getSpecificRestaurantName('breakfast', dayIndex)}`,
        location: `${getSpecificRestaurantName('breakfast', dayIndex)}, ${destination} City Center`,
        description: `Start day ${dayNumber} with an authentic local breakfast experience at one of ${destination}'s most beloved cafés.`,
      },
      {
        time: '09:00',
        activity: `${destination} Morning Market Visit`,
        location: `Central Market, ${destination}`,
        description: `Explore the vibrant local market and experience the morning bustle of ${destination} life.`,
      },
      {
        time: '10:30',
        activity: `Visit ${getSpecificAttraction('landmark', dayIndex)}`,
        location: `${getSpecificAttraction('landmark', dayIndex)}, ${destination}`,
        description: `Discover one of ${destination}'s most iconic landmarks and learn about its fascinating history.`,
      },
      {
        time: '12:30',
        activity: `Lunch at ${getSpecificRestaurantName('lunch', dayIndex)}`,
        location: `${getSpecificRestaurantName('lunch', dayIndex)}, ${destination}`,
        description: `Enjoy an authentic local lunch featuring ${country} regional specialties and traditional cuisine.`,
      },
      {
        time: '14:00',
        activity: `${getSpecificAttraction('museum', dayIndex)} Visit`,
        location: `${getSpecificAttraction('museum', dayIndex)}, ${destination}`,
        description: `Visit ${destination}'s premier museum to learn about local history, art, and culture.`,
      },
      {
        time: '16:00',
        activity: `Explore ${getSpecificAttraction('district', dayIndex)}`,
        location: `${getSpecificAttraction('district', dayIndex)}, ${destination}`,
        description: `Wander through the charming ${getSpecificAttraction('district', dayIndex)} area with its unique shops and local atmosphere.`,
      },
      {
        time: '18:30',
        activity: `${destination} Sunset Viewpoint`,
        location: `Best Viewpoint in ${destination}`,
        description: `Visit a scenic location to watch the sunset and enjoy panoramic views of ${destination}.`,
      },
      {
        time: '19:30',
        activity: `Dinner at ${getSpecificRestaurantName('dinner', dayIndex)}`,
        location: `${getSpecificRestaurantName('dinner', dayIndex)}, ${destination}`,
        description: `Experience fine dining with contemporary ${country} cuisine and local wine pairings.`,
      },
      {
        time: '21:30',
        activity: `${destination} Evening Entertainment`,
        location: `Entertainment Quarter, ${destination}`,
        description: `Explore ${destination}'s nightlife scene with bars, live music venues, or cultural performances.`,
      },
      {
        time: '23:00',
        activity: `Night Walk Through ${destination}`,
        location: `Illuminated City Center, ${destination}`,
        description: `End the day with a peaceful walk through the beautifully lit streets of ${destination}.`,
      },
    ];
  }

  /**
   * Check if an activity is specific (has real names) or generic
   */
  private isActivitySpecific(activity: any): boolean {
    const genericTerms = [
      'local',
      'traditional',
      'main',
      'central',
      'historic',
      'popular',
      'famous',
      'typical',
      'authentic',
      'recommended',
      'best',
      'top',
      'city center',
      'downtown',
      'old town',
      'market',
      'museum',
      'restaurant',
      'café',
      'hotel',
      'attraction',
      'landmark',
      'district',
      'quarter',
      'neighborhood',
      'area',
      'region',
      'zone',
      'place',
      'spot',
      'location',
    ];

    const activityText =
      `${activity.activity} ${activity.location}`.toLowerCase();

    // Check if the text contains mostly generic terms
    const hasSpecificName = !genericTerms.some(
      (term) =>
        activityText.includes(term) &&
        !activityText.match(new RegExp(`\\b[A-Z][a-z]+ ${term}\\b`, 'i')), // Allow "Louvre Museum" but not "local museum"
    );

    // Check for specific indicators (proper nouns, specific addresses, etc.)
    const hasProperNoun =
      /\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/.test(activity.activity) ||
      /\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/.test(activity.location);

    return hasSpecificName && hasProperNoun;
  }

  /**
   * Search for activity locations using map search
   */
  private async searchActivityLocations(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
  ): Promise<any> {
    try {
      // Import map_search tool
      const { map_search } = await import('@/lib/ai/tools/map-search');

      // Create a mock session and dataStream for the map_search tool
      const mockSession = {
        user: { id: 'system', type: 'guest' as const },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };
      const mockDataStream: DataStreamWriter = {
        writeMessageAnnotation: () => {},
        write: () => '',
        writeData: () => '',
        writeSource: () => '',
        merge: () => '',
        onError: () => '',
      };

      const mapSearchTool = map_search({
        session: mockSession,
        dataStream: mockDataStream,
      });

      // Create search queries for activity locations
      const locationQueries = [
        `attractions in ${destinationInfo.destination} ${destinationInfo.country}`,
        `activities ${destinationInfo.destination}`,
      ];

      // Add preference-specific location queries
      if (userPreferences.activities.interests.length > 0) {
        locationQueries.push(
          `${userPreferences.activities.interests[0]} locations in ${destinationInfo.destination}`,
        );
      }

      // Execute all location searches in parallel
      const locationPromises = locationQueries.map((query) =>
        (mapSearchTool.execute as any)({
          query,
          includeDetails: true,
        }),
      );

      const results = await Promise.all(locationPromises);
      return results;
    } catch (error) {
      console.error('Error searching activity locations:', error);
      return { results: [] };
    }
  }
}
