import { z } from 'zod';
import { generateObject, type LanguageModelV1 } from 'ai';
import type { DestinationInfo, DayItinerary } from '../types';
import type { UserPreferences } from './preference-agent';
import type { LocalRecommendations } from './local-expert-agent';
import { web_search } from '@/lib/ai/tools/web-search';
import type { DataStreamWriter } from 'ai';
import {
  withAIModelResilience,
  createMockGenerateObjectResult,
} from '../api-resilience';

/**
 * Interface for activity options
 */
export interface ActivityOption {
  name: string;
  description: string;
  duration: string;
  cost: string;
  bestFor: string[];
  accessibility: string;
  bookingInfo?: string;
  coordinates: {
    lat: string;
    lng: string;
  };
}

/**
 * Interface for activity plan
 */
export interface ActivityPlan {
  days: DayItinerary[];
  alternativeActivities: {
    day: number;
    options: ActivityOption[];
  }[];
  rainyDayOptions: ActivityOption[];
  eveningEntertainment: ActivityOption[];
  familyFriendlyOptions?: ActivityOption[];
  accessibleOptions?: ActivityOption[];
  sustainableOptions?: ActivityOption[];
}

/**
 * ActivityPlannerAgent is responsible for creating a personalized activity plan
 * based on user preferences and local recommendations.
 */
export class ActivityPlannerAgent {
  private model: LanguageModelV1;

  constructor(model: LanguageModelV1) {
    this.model = model;
  }

  /**
   * Create a personalized activity plan
   */
  async createActivityPlan(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
    localRecommendations: LocalRecommendations,
  ): Promise<ActivityPlan> {
    try {
      console.log('Creating activity plan for:', destinationInfo.destination);

      // Check if duration is null or undefined
      if (
        destinationInfo.duration === null ||
        destinationInfo.duration === undefined
      ) {
        console.error(
          'Duration is null or undefined. Cannot create activity plan.',
        );
        return this.getFallbackActivityPlan(destinationInfo);
      }

      // Execute all tool searches in parallel for better performance
      const [searchResults, locationResults] = await Promise.all([
        this.searchActivities(destinationInfo, userPreferences),
        this.searchActivityLocations(destinationInfo, userPreferences),
      ]);

      // Generate activity plan using the model with enhanced error handling
      const generateObjectResult = await withAIModelResilience(
        () =>
          generateObject({
            model: this.model,
            system: `You are an expert activity planner who specializes in creating comprehensive, detailed itineraries.
          Create a full day-by-day activity plan with 8-12 activities per day, including:
          - Early morning activities (7:00-9:00 AM)
          - Morning activities and sightseeing (9:00-12:00 PM)
          - Lunch at specific restaurants with names and locations
          - Afternoon activities and cultural visits (2:00-5:00 PM)
          - Evening activities and entertainment (5:00-8:00 PM)
          - Dinner at specific restaurants with cuisine types
          - Night activities, bars, or evening strolls (8:00-11:00 PM)

          Include specific restaurant names, exact locations, activity durations, and detailed descriptions.
          Consider realistic travel times between locations and provide comprehensive daily experiences.
          Each day should feel complete from morning to night with varied activities and dining options.`,
            prompt: `Create a personalized activity plan for ${destinationInfo.destination}, ${destinationInfo.country}
        for ${destinationInfo.duration} days based on these user preferences:

        ${JSON.stringify(userPreferences, null, 2)}

        Consider these local recommendations:
        ${JSON.stringify(localRecommendations, null, 2)}

        And these activity search results:
        Web Search Results: ${JSON.stringify(searchResults, null, 2)}
        Location Results: ${JSON.stringify(locationResults, null, 2)}

        Create a comprehensive day-by-day plan with 8-12 activities per day covering the entire day:

        FOR EACH DAY INCLUDE:

        MORNING (7:00-12:00):
        - Specific breakfast restaurant with name and exact location
        - 2-3 morning activities (sightseeing, cultural visits, markets, temples)

        AFTERNOON (12:00-17:00):
        - Specific lunch restaurant with cuisine type and exact location
        - 2-3 afternoon activities (museums, parks, shopping districts, tours)

        EVENING/NIGHT (17:00-23:00):
        - Specific dinner restaurant with cuisine type and exact location
        - 2-3 evening/night activities (entertainment, bars, night markets, scenic walks, shows)

        REQUIREMENTS:
        - Use SPECIFIC restaurant names, not generic terms like "local restaurant"
        - Provide exact addresses and neighborhoods when possible
        - Include realistic activity durations and travel times
        - Mix cultural, culinary, entertainment, shopping, and relaxation activities
        - Include both popular attractions and hidden local gems
        - Consider local specialties and must-try experiences
        - Make each day feel complete from morning to night

        ${userPreferences.travelWithChildren ? 'Include family-friendly options suitable for children.' : ''}
        ${userPreferences.accessibility.mobilityIssues ? 'Prioritize accessible options and consider mobility issues.' : ''}
        ${userPreferences.sustainabilityPreference ? 'Include sustainable and eco-friendly activity options.' : ''}`,
            schema: z.object({
              days: z
                .array(
                  z.object({
                    day: z.number().describe('Day number'),
                    activities: z
                      .array(
                        z.object({
                          time: z
                            .string()
                            .describe('Time of the activity (e.g., "09:00")'),
                          activity: z
                            .string()
                            .describe(
                              'Specific name of the activity or restaurant',
                            ),
                          location: z
                            .string()
                            .describe(
                              'Exact location with address or neighborhood',
                            ),
                          description: z
                            .string()
                            .describe('Detailed description of the activity'),
                          poiIndex: z
                            .number()
                            .optional()
                            .describe('Reference to the POI in the POIs array'),
                        }),
                      )
                      .min(8)
                      .max(12)
                      .describe(
                        '8-12 activities covering the full day from morning to night',
                      ),
                  }),
                )
                .describe('Day-by-day activity plan'),

              alternativeActivities: z
                .array(
                  z.object({
                    day: z.number().describe('Day number'),
                    options: z.array(
                      z.object({
                        name: z.string().describe('Name of the activity'),
                        description: z
                          .string()
                          .describe('Description of the activity'),
                        duration: z.string().describe('Estimated duration'),
                        cost: z.string().describe('Estimated cost'),
                        bestFor: z
                          .array(z.string())
                          .describe('What this activity is best for'),
                        accessibility: z
                          .string()
                          .describe('Accessibility information'),
                        bookingInfo: z
                          .string()
                          .optional()
                          .describe('Booking information if needed'),
                        coordinates: z.object({
                          lat: z.string().describe('Latitude coordinate'),
                          lng: z.string().describe('Longitude coordinate'),
                        }),
                      }),
                    ),
                  }),
                )
                .describe('Alternative activity options for each day'),

              rainyDayOptions: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the activity'),
                    description: z
                      .string()
                      .describe('Description of the activity'),
                    duration: z.string().describe('Estimated duration'),
                    cost: z.string().describe('Estimated cost'),
                    bestFor: z
                      .array(z.string())
                      .describe('What this activity is best for'),
                    accessibility: z
                      .string()
                      .describe('Accessibility information'),
                    bookingInfo: z
                      .string()
                      .optional()
                      .describe('Booking information if needed'),
                    coordinates: z.object({
                      lat: z.string().describe('Latitude coordinate'),
                      lng: z.string().describe('Longitude coordinate'),
                    }),
                  }),
                )
                .describe('Activity options for rainy days'),

              eveningEntertainment: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the activity'),
                    description: z
                      .string()
                      .describe('Description of the activity'),
                    duration: z.string().describe('Estimated duration'),
                    cost: z.string().describe('Estimated cost'),
                    bestFor: z
                      .array(z.string())
                      .describe('What this activity is best for'),
                    accessibility: z
                      .string()
                      .describe('Accessibility information'),
                    bookingInfo: z
                      .string()
                      .optional()
                      .describe('Booking information if needed'),
                    coordinates: z.object({
                      lat: z.string().describe('Latitude coordinate'),
                      lng: z.string().describe('Longitude coordinate'),
                    }),
                  }),
                )
                .describe('Evening entertainment options'),

              familyFriendlyOptions: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the activity'),
                    description: z
                      .string()
                      .describe('Description of the activity'),
                    duration: z.string().describe('Estimated duration'),
                    cost: z.string().describe('Estimated cost'),
                    bestFor: z
                      .array(z.string())
                      .describe('What this activity is best for'),
                    accessibility: z
                      .string()
                      .describe('Accessibility information'),
                    bookingInfo: z
                      .string()
                      .optional()
                      .describe('Booking information if needed'),
                    coordinates: z.object({
                      lat: z.string().describe('Latitude coordinate'),
                      lng: z.string().describe('Longitude coordinate'),
                    }),
                  }),
                )
                .optional()
                .describe('Family-friendly activity options'),

              accessibleOptions: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the activity'),
                    description: z
                      .string()
                      .describe('Description of the activity'),
                    duration: z.string().describe('Estimated duration'),
                    cost: z.string().describe('Estimated cost'),
                    bestFor: z
                      .array(z.string())
                      .describe('What this activity is best for'),
                    accessibility: z
                      .string()
                      .describe('Accessibility information'),
                    bookingInfo: z
                      .string()
                      .optional()
                      .describe('Booking information if needed'),
                    coordinates: z.object({
                      lat: z.string().describe('Latitude coordinate'),
                      lng: z.string().describe('Longitude coordinate'),
                    }),
                  }),
                )
                .optional()
                .describe('Accessible activity options'),

              sustainableOptions: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the activity'),
                    description: z
                      .string()
                      .describe('Description of the activity'),
                    duration: z.string().describe('Estimated duration'),
                    cost: z.string().describe('Estimated cost'),
                    bestFor: z
                      .array(z.string())
                      .describe('What this activity is best for'),
                    accessibility: z
                      .string()
                      .describe('Accessibility information'),
                    bookingInfo: z
                      .string()
                      .optional()
                      .describe('Booking information if needed'),
                    coordinates: z.object({
                      lat: z.string().describe('Latitude coordinate'),
                      lng: z.string().describe('Longitude coordinate'),
                    }),
                  }),
                )
                .optional()
                .describe('Sustainable and eco-friendly activity options'),
            }),
            temperature: 0.7,
          }),
        'gemini-2.0-flash',
        'activity-plan-generation',
        () =>
          Promise.resolve(
            createMockGenerateObjectResult(
              this.getFallbackActivityPlan(destinationInfo),
            ),
          ),
      );

      return generateObjectResult.object;
    } catch (error) {
      console.error('Error creating activity plan:', error);

      // Return fallback activity plan if generation fails
      return this.getFallbackActivityPlan(destinationInfo);
    }
  }

  /**
   * Search for activities in the destination
   */
  private async searchActivities(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
  ): Promise<any> {
    try {
      // Create a mock session and dataStream for the web_search tool
      const mockSession = {
        user: { id: 'system', type: 'guest' as const },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };
      // Create a mock DataStreamWriter
      const mockDataStream: DataStreamWriter = {
        writeMessageAnnotation: () => {},
        write: () => '',
        writeData: () => '',
        writeSource: () => '',
        merge: () => '',
        onError: () => '',
      };

      // Use the web_search tool to get activity information
      const searchTool = web_search({
        session: mockSession,
        dataStream: mockDataStream,
      });

      // Create search queries based on user preferences
      const queries = [
        `top attractions in ${destinationInfo.destination} ${destinationInfo.country}`,
        `${userPreferences.activities.activityLevel} activities in ${destinationInfo.destination}`,
      ];

      // Add preference-specific queries
      if (userPreferences.travelWithChildren) {
        queries.push(
          `family-friendly activities in ${destinationInfo.destination}`,
        );
      }

      if (userPreferences.accessibility.mobilityIssues) {
        queries.push(
          `accessible activities in ${destinationInfo.destination} for mobility issues`,
        );
      }

      if (userPreferences.sustainabilityPreference) {
        queries.push(
          `sustainable eco-friendly activities in ${destinationInfo.destination}`,
        );
      }

      // Add activity interest queries
      for (const interest of userPreferences.activities.interests.slice(0, 2)) {
        queries.push(
          `${interest} in ${destinationInfo.destination} ${destinationInfo.country}`,
        );
      }

      // Execute all search queries in parallel
      const searchPromises = queries.map((query) =>
        (searchTool.execute as any)({
          queries: [query],
          maxResults: [3],
          topics: ['general'],
        }),
      );

      const results = await Promise.all(searchPromises);
      return results;
    } catch (error) {
      console.error('Error searching activities:', error);
      return { results: [] };
    }
  }

  /**
   * Get fallback activity plan if generation fails
   */
  private getFallbackActivityPlan(
    destinationInfo: DestinationInfo,
  ): ActivityPlan {
    const days: DayItinerary[] = [];

    // Use a default duration of 3 days if duration is null
    const actualDuration =
      destinationInfo.duration !== null ? destinationInfo.duration : 3;
    console.log(
      `Using fallback duration of ${actualDuration} days for activity plan generation`,
    );

    // Create a basic activity plan for each day
    for (let i = 1; i <= actualDuration; i++) {
      days.push({
        day: i,
        activities: [
          {
            time: '07:30',
            activity: 'Traditional Breakfast at Local Café',
            location: `Traditional Café in ${destinationInfo.destination} City Center`,
            description:
              'Start your day with an authentic local breakfast experience at a traditional café.',
          },
          {
            time: '09:00',
            activity: 'Morning Market Visit',
            location: `Central Market, ${destinationInfo.destination}`,
            description:
              'Explore the vibrant local market and experience the morning bustle of local life.',
          },
          {
            time: '10:30',
            activity: 'Historical District Walking Tour',
            location: `Historic Quarter, ${destinationInfo.destination}`,
            description:
              'Take a self-guided walking tour through the historic district and main attractions.',
          },
          {
            time: '12:30',
            activity: 'Lunch at Traditional Restaurant',
            location: `Traditional Restaurant, ${destinationInfo.destination}`,
            description:
              'Enjoy an authentic local lunch featuring regional specialties and traditional cuisine.',
          },
          {
            time: '14:00',
            activity: 'Cultural Museum Visit',
            location: `Main Museum, ${destinationInfo.destination}`,
            description:
              "Visit the city's premier museum to learn about local history, art, and culture.",
          },
          {
            time: '16:00',
            activity: 'Local Shopping District',
            location: `Shopping District, ${destinationInfo.destination}`,
            description:
              'Explore local shops, boutiques, and artisan stores for unique souvenirs.',
          },
          {
            time: '18:30',
            activity: 'Sunset Viewpoint',
            location: `Scenic Viewpoint, ${destinationInfo.destination}`,
            description:
              'Visit a scenic location to watch the sunset and enjoy panoramic city views.',
          },
          {
            time: '19:30',
            activity: 'Dinner at Fine Dining Restaurant',
            location: `Upscale Restaurant, ${destinationInfo.destination}`,
            description:
              'Experience fine dining with contemporary cuisine and local wine pairings.',
          },
          {
            time: '21:30',
            activity: 'Evening Entertainment District',
            location: `Entertainment Quarter, ${destinationInfo.destination}`,
            description:
              'Explore the nightlife scene with bars, live music venues, or cultural performances.',
          },
          {
            time: '23:00',
            activity: 'Night Walk & City Lights',
            location: `Illuminated City Center, ${destinationInfo.destination}`,
            description:
              'End the day with a peaceful walk through the beautifully lit city center.',
          },
        ],
      });
    }

    return {
      days,
      alternativeActivities: [
        {
          day: 1,
          options: [
            {
              name: 'Park Visit',
              description: 'Visit a local park for relaxation.',
              duration: '2 hours',
              cost: 'Free',
              bestFor: ['Relaxation', 'Nature lovers'],
              accessibility: 'Generally accessible',
              coordinates: {
                lat: destinationInfo.coordinates.lat,
                lng: destinationInfo.coordinates.lng,
              },
            },
          ],
        },
      ],
      rainyDayOptions: [
        {
          name: 'Museum Visit',
          description: 'Visit a local museum to stay dry.',
          duration: '3 hours',
          cost: 'Moderate',
          bestFor: ['Rainy days', 'Culture enthusiasts'],
          accessibility: 'Generally accessible',
          coordinates: {
            lat: destinationInfo.coordinates.lat,
            lng: destinationInfo.coordinates.lng,
          },
        },
      ],
      eveningEntertainment: [
        {
          name: 'Local Performance',
          description: 'Enjoy a local music or theater performance.',
          duration: '2 hours',
          cost: 'Moderate',
          bestFor: ['Evening entertainment', 'Cultural experience'],
          accessibility: 'Generally accessible',
          coordinates: {
            lat: destinationInfo.coordinates.lat,
            lng: destinationInfo.coordinates.lng,
          },
        },
      ],
    };
  }

  /**
   * Search for activity locations using map search
   */
  private async searchActivityLocations(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
  ): Promise<any> {
    try {
      // Import map_search tool
      const { map_search } = await import('@/lib/ai/tools/map-search');

      // Create a mock session and dataStream for the map_search tool
      const mockSession = {
        user: { id: 'system', type: 'guest' as const },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };
      const mockDataStream: DataStreamWriter = {
        writeMessageAnnotation: () => {},
        write: () => '',
        writeData: () => '',
        writeSource: () => '',
        merge: () => '',
        onError: () => '',
      };

      const mapSearchTool = map_search({
        session: mockSession,
        dataStream: mockDataStream,
      });

      // Create search queries for activity locations
      const locationQueries = [
        `attractions in ${destinationInfo.destination} ${destinationInfo.country}`,
        `activities ${destinationInfo.destination}`,
      ];

      // Add preference-specific location queries
      if (userPreferences.activities.interests.length > 0) {
        locationQueries.push(
          `${userPreferences.activities.interests[0]} locations in ${destinationInfo.destination}`,
        );
      }

      // Execute all location searches in parallel
      const locationPromises = locationQueries.map((query) =>
        (mapSearchTool.execute as any)({
          query,
          includeDetails: true,
        }),
      );

      const results = await Promise.all(locationPromises);
      return results;
    } catch (error) {
      console.error('Error searching activity locations:', error);
      return { results: [] };
    }
  }
}
