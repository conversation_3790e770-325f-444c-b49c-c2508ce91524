import { z } from 'zod';
import { generateObject, type LanguageModelV1 } from 'ai';
import type { DestinationInfo, DayItinerary } from '../types';
import type { UserPreferences } from './preference-agent';
import type { LocalRecommendations } from './local-expert-agent';
import { web_search } from '@/lib/ai/tools/web-search';
import type { DataStreamWriter } from 'ai';
import {
  withAIModelResilience,
  createMockGenerateObjectResult,
} from '../api-resilience';

/**
 * Interface for activity options
 */
export interface ActivityOption {
  name: string;
  description: string;
  duration: string;
  cost: string;
  bestFor: string[];
  accessibility: string;
  bookingInfo?: string;
  coordinates: {
    lat: string;
    lng: string;
  };
}

/**
 * Interface for activity plan
 */
export interface ActivityPlan {
  days: DayItinerary[];
  alternativeActivities: {
    day: number;
    options: ActivityOption[];
  }[];
  rainyDayOptions: ActivityOption[];
  eveningEntertainment: ActivityOption[];
  familyFriendlyOptions?: ActivityOption[];
  accessibleOptions?: ActivityOption[];
  sustainableOptions?: ActivityOption[];
}

/**
 * ActivityPlannerAgent is responsible for creating a personalized activity plan
 * based on user preferences and local recommendations.
 */
export class ActivityPlannerAgent {
  private model: LanguageModelV1;

  constructor(model: LanguageModelV1) {
    this.model = model;
  }

  /**
   * Create a personalized activity plan
   */
  async createActivityPlan(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
    localRecommendations: LocalRecommendations,
  ): Promise<ActivityPlan> {
    try {
      console.log('Creating activity plan for:', destinationInfo.destination);

      // Check if duration is null or undefined
      if (
        destinationInfo.duration === null ||
        destinationInfo.duration === undefined
      ) {
        console.error(
          'Duration is null or undefined. Cannot create activity plan.',
        );
        return this.getFallbackActivityPlan(destinationInfo);
      }

      // Execute all tool searches in parallel for better performance
      const [searchResults, locationResults] = await Promise.all([
        this.searchActivities(destinationInfo, userPreferences),
        this.searchActivityLocations(destinationInfo, userPreferences),
      ]);

      // Generate activity plan using the model with enhanced error handling
      const generateObjectResult = await withAIModelResilience(
        () =>
          generateObject({
            model: this.model,
            system: `You are an expert activity planner who specializes in creating personalized itineraries.
          Create a detailed day-by-day activity plan that perfectly matches the user's preferences.
          Include a variety of activities with accurate timing, locations, and descriptions.
          Consider the logistics of the destination and realistic travel times between activities.
          Provide alternative options for flexibility and contingency plans for bad weather.
          If the user has specific needs (accessibility, family-friendly, sustainability), prioritize those.`,
            prompt: `Create a personalized activity plan for ${destinationInfo.destination}, ${destinationInfo.country}
        for ${destinationInfo.duration} days based on these user preferences:

        ${JSON.stringify(userPreferences, null, 2)}

        Consider these local recommendations:
        ${JSON.stringify(localRecommendations, null, 2)}

        And these activity search results:
        Web Search Results: ${JSON.stringify(searchResults, null, 2)}
        Location Results: ${JSON.stringify(locationResults, null, 2)}

        Create a detailed day-by-day plan with:
        - A logical sequence of activities for each day
        - Realistic timing that accounts for travel between locations
        - A mix of activities that match the user's interests and activity level
        - Alternative options for flexibility
        - Rainy day contingency options
        - Evening entertainment options

        ${userPreferences.travelWithChildren ? 'Include family-friendly options suitable for children.' : ''}
        ${userPreferences.accessibility.mobilityIssues ? 'Prioritize accessible options and consider mobility issues.' : ''}
        ${userPreferences.sustainabilityPreference ? 'Include sustainable and eco-friendly activity options.' : ''}`,
            schema: z.object({
              days: z
                .array(
                  z.object({
                    day: z.number().describe('Day number'),
                    activities: z.array(
                      z.object({
                        time: z.string().describe('Time of the activity'),
                        activity: z.string().describe('Name of the activity'),
                        location: z
                          .string()
                          .describe('Location of the activity'),
                        description: z
                          .string()
                          .describe('Description of the activity'),
                        poiIndex: z
                          .number()
                          .optional()
                          .describe('Reference to the POI in the POIs array'),
                      }),
                    ),
                  }),
                )
                .describe('Day-by-day activity plan'),

              alternativeActivities: z
                .array(
                  z.object({
                    day: z.number().describe('Day number'),
                    options: z.array(
                      z.object({
                        name: z.string().describe('Name of the activity'),
                        description: z
                          .string()
                          .describe('Description of the activity'),
                        duration: z.string().describe('Estimated duration'),
                        cost: z.string().describe('Estimated cost'),
                        bestFor: z
                          .array(z.string())
                          .describe('What this activity is best for'),
                        accessibility: z
                          .string()
                          .describe('Accessibility information'),
                        bookingInfo: z
                          .string()
                          .optional()
                          .describe('Booking information if needed'),
                        coordinates: z.object({
                          lat: z.string().describe('Latitude coordinate'),
                          lng: z.string().describe('Longitude coordinate'),
                        }),
                      }),
                    ),
                  }),
                )
                .describe('Alternative activity options for each day'),

              rainyDayOptions: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the activity'),
                    description: z
                      .string()
                      .describe('Description of the activity'),
                    duration: z.string().describe('Estimated duration'),
                    cost: z.string().describe('Estimated cost'),
                    bestFor: z
                      .array(z.string())
                      .describe('What this activity is best for'),
                    accessibility: z
                      .string()
                      .describe('Accessibility information'),
                    bookingInfo: z
                      .string()
                      .optional()
                      .describe('Booking information if needed'),
                    coordinates: z.object({
                      lat: z.string().describe('Latitude coordinate'),
                      lng: z.string().describe('Longitude coordinate'),
                    }),
                  }),
                )
                .describe('Activity options for rainy days'),

              eveningEntertainment: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the activity'),
                    description: z
                      .string()
                      .describe('Description of the activity'),
                    duration: z.string().describe('Estimated duration'),
                    cost: z.string().describe('Estimated cost'),
                    bestFor: z
                      .array(z.string())
                      .describe('What this activity is best for'),
                    accessibility: z
                      .string()
                      .describe('Accessibility information'),
                    bookingInfo: z
                      .string()
                      .optional()
                      .describe('Booking information if needed'),
                    coordinates: z.object({
                      lat: z.string().describe('Latitude coordinate'),
                      lng: z.string().describe('Longitude coordinate'),
                    }),
                  }),
                )
                .describe('Evening entertainment options'),

              familyFriendlyOptions: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the activity'),
                    description: z
                      .string()
                      .describe('Description of the activity'),
                    duration: z.string().describe('Estimated duration'),
                    cost: z.string().describe('Estimated cost'),
                    bestFor: z
                      .array(z.string())
                      .describe('What this activity is best for'),
                    accessibility: z
                      .string()
                      .describe('Accessibility information'),
                    bookingInfo: z
                      .string()
                      .optional()
                      .describe('Booking information if needed'),
                    coordinates: z.object({
                      lat: z.string().describe('Latitude coordinate'),
                      lng: z.string().describe('Longitude coordinate'),
                    }),
                  }),
                )
                .optional()
                .describe('Family-friendly activity options'),

              accessibleOptions: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the activity'),
                    description: z
                      .string()
                      .describe('Description of the activity'),
                    duration: z.string().describe('Estimated duration'),
                    cost: z.string().describe('Estimated cost'),
                    bestFor: z
                      .array(z.string())
                      .describe('What this activity is best for'),
                    accessibility: z
                      .string()
                      .describe('Accessibility information'),
                    bookingInfo: z
                      .string()
                      .optional()
                      .describe('Booking information if needed'),
                    coordinates: z.object({
                      lat: z.string().describe('Latitude coordinate'),
                      lng: z.string().describe('Longitude coordinate'),
                    }),
                  }),
                )
                .optional()
                .describe('Accessible activity options'),

              sustainableOptions: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the activity'),
                    description: z
                      .string()
                      .describe('Description of the activity'),
                    duration: z.string().describe('Estimated duration'),
                    cost: z.string().describe('Estimated cost'),
                    bestFor: z
                      .array(z.string())
                      .describe('What this activity is best for'),
                    accessibility: z
                      .string()
                      .describe('Accessibility information'),
                    bookingInfo: z
                      .string()
                      .optional()
                      .describe('Booking information if needed'),
                    coordinates: z.object({
                      lat: z.string().describe('Latitude coordinate'),
                      lng: z.string().describe('Longitude coordinate'),
                    }),
                  }),
                )
                .optional()
                .describe('Sustainable and eco-friendly activity options'),
            }),
            temperature: 0.7,
          }),
        'gemini-2.0-flash',
        'activity-plan-generation',
        () =>
          Promise.resolve(
            createMockGenerateObjectResult(
              this.getFallbackActivityPlan(destinationInfo),
            ),
          ),
      );

      return generateObjectResult.object;
    } catch (error) {
      console.error('Error creating activity plan:', error);

      // Return fallback activity plan if generation fails
      return this.getFallbackActivityPlan(destinationInfo);
    }
  }

  /**
   * Search for activities in the destination
   */
  private async searchActivities(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
  ): Promise<any> {
    try {
      // Create a mock session and dataStream for the web_search tool
      const mockSession = {
        user: { id: 'system', type: 'guest' as const },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };
      // Create a mock DataStreamWriter
      const mockDataStream: DataStreamWriter = {
        writeMessageAnnotation: () => {},
        write: () => '',
        writeData: () => '',
        writeSource: () => '',
        merge: () => '',
        onError: () => '',
      };

      // Use the web_search tool to get activity information
      const searchTool = web_search({
        session: mockSession,
        dataStream: mockDataStream,
      });

      // Create search queries based on user preferences
      const queries = [
        `top attractions in ${destinationInfo.destination} ${destinationInfo.country}`,
        `${userPreferences.activities.activityLevel} activities in ${destinationInfo.destination}`,
      ];

      // Add preference-specific queries
      if (userPreferences.travelWithChildren) {
        queries.push(
          `family-friendly activities in ${destinationInfo.destination}`,
        );
      }

      if (userPreferences.accessibility.mobilityIssues) {
        queries.push(
          `accessible activities in ${destinationInfo.destination} for mobility issues`,
        );
      }

      if (userPreferences.sustainabilityPreference) {
        queries.push(
          `sustainable eco-friendly activities in ${destinationInfo.destination}`,
        );
      }

      // Add activity interest queries
      for (const interest of userPreferences.activities.interests.slice(0, 2)) {
        queries.push(
          `${interest} in ${destinationInfo.destination} ${destinationInfo.country}`,
        );
      }

      // Execute all search queries in parallel
      const searchPromises = queries.map((query) =>
        (searchTool.execute as any)({
          queries: [query],
          maxResults: [3],
          topics: ['general'],
        }),
      );

      const results = await Promise.all(searchPromises);
      return results;
    } catch (error) {
      console.error('Error searching activities:', error);
      return { results: [] };
    }
  }

  /**
   * Get fallback activity plan if generation fails
   */
  private getFallbackActivityPlan(
    destinationInfo: DestinationInfo,
  ): ActivityPlan {
    const days: DayItinerary[] = [];

    // Use a default duration of 3 days if duration is null
    const actualDuration =
      destinationInfo.duration !== null ? destinationInfo.duration : 3;
    console.log(
      `Using fallback duration of ${actualDuration} days for activity plan generation`,
    );

    // Create a basic activity plan for each day
    for (let i = 1; i <= actualDuration; i++) {
      days.push({
        day: i,
        activities: [
          {
            time: '09:00',
            activity: 'Breakfast',
            location: 'Local Cafe',
            description:
              'Start the day with a delicious breakfast at a local cafe.',
          },
          {
            time: '10:30',
            activity: 'City Exploration',
            location: 'City Center',
            description: 'Explore the main attractions in the city center.',
          },
          {
            time: '13:00',
            activity: 'Lunch',
            location: 'Local Restaurant',
            description: 'Enjoy lunch at a popular local restaurant.',
          },
          {
            time: '15:00',
            activity: 'Cultural Visit',
            location: 'Museum or Historical Site',
            description:
              'Visit a museum or historical site to learn about the local culture.',
          },
          {
            time: '18:00',
            activity: 'Dinner',
            location: 'Restaurant',
            description: 'Have dinner at a recommended restaurant.',
          },
          {
            time: '20:00',
            activity: 'Evening Stroll',
            location: 'City Center',
            description:
              'Take a relaxing evening stroll to see the city lights.',
          },
        ],
      });
    }

    return {
      days,
      alternativeActivities: [
        {
          day: 1,
          options: [
            {
              name: 'Park Visit',
              description: 'Visit a local park for relaxation.',
              duration: '2 hours',
              cost: 'Free',
              bestFor: ['Relaxation', 'Nature lovers'],
              accessibility: 'Generally accessible',
              coordinates: {
                lat: destinationInfo.coordinates.lat,
                lng: destinationInfo.coordinates.lng,
              },
            },
          ],
        },
      ],
      rainyDayOptions: [
        {
          name: 'Museum Visit',
          description: 'Visit a local museum to stay dry.',
          duration: '3 hours',
          cost: 'Moderate',
          bestFor: ['Rainy days', 'Culture enthusiasts'],
          accessibility: 'Generally accessible',
          coordinates: {
            lat: destinationInfo.coordinates.lat,
            lng: destinationInfo.coordinates.lng,
          },
        },
      ],
      eveningEntertainment: [
        {
          name: 'Local Performance',
          description: 'Enjoy a local music or theater performance.',
          duration: '2 hours',
          cost: 'Moderate',
          bestFor: ['Evening entertainment', 'Cultural experience'],
          accessibility: 'Generally accessible',
          coordinates: {
            lat: destinationInfo.coordinates.lat,
            lng: destinationInfo.coordinates.lng,
          },
        },
      ],
    };
  }

  /**
   * Search for activity locations using map search
   */
  private async searchActivityLocations(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
  ): Promise<any> {
    try {
      // Import map_search tool
      const { map_search } = await import('@/lib/ai/tools/map-search');

      // Create a mock session and dataStream for the map_search tool
      const mockSession = {
        user: { id: 'system', type: 'guest' as const },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };
      const mockDataStream: DataStreamWriter = {
        writeMessageAnnotation: () => {},
        write: () => '',
        writeData: () => '',
        writeSource: () => '',
        merge: () => '',
        onError: () => '',
      };

      const mapSearchTool = map_search({
        session: mockSession,
        dataStream: mockDataStream,
      });

      // Create search queries for activity locations
      const locationQueries = [
        `attractions in ${destinationInfo.destination} ${destinationInfo.country}`,
        `activities ${destinationInfo.destination}`,
      ];

      // Add preference-specific location queries
      if (userPreferences.activities.interests.length > 0) {
        locationQueries.push(
          `${userPreferences.activities.interests[0]} locations in ${destinationInfo.destination}`,
        );
      }

      // Execute all location searches in parallel
      const locationPromises = locationQueries.map((query) =>
        (mapSearchTool.execute as any)({
          query,
          includeDetails: true,
        }),
      );

      const results = await Promise.all(locationPromises);
      return results;
    } catch (error) {
      console.error('Error searching activity locations:', error);
      return { results: [] };
    }
  }
}
