import React from 'react';
import { cn } from '@/lib/utils';
import type { IconProps } from '@phosphor-icons/react';

interface SearchLoadingStateProps {
  icon: React.ForwardRefExoticComponent<
    IconProps & React.RefAttributes<SVGSVGElement>
  >;
  text: string;
  color: string;
}

export const SearchLoadingState: React.FC<SearchLoadingStateProps> = ({
  icon: Icon,
  text,
  color,
}) => {
  return (
    <div className="flex flex-col items-center justify-center py-8">
      <div
        className={cn(
          'h-10 w-10 rounded-full flex items-center justify-center mb-4',
          `bg-${color}-100 dark:bg-${color}-900/30`,
        )}
      >
        <Icon className={`size-5 text-${color}-500`} weight="duotone" />
      </div>
      <p className="text-sm text-neutral-600 dark:text-neutral-400">{text}</p>
    </div>
  );
};
