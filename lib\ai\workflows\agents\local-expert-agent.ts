import { z } from 'zod';
import { generateObject, type DataStreamWriter } from 'ai';
import type { LanguageModelV1 } from 'ai';
import type { DestinationInfo } from '../types';
import type { UserPreferences } from './preference-agent';
import { web_search } from '@/lib/ai/tools/web-search';

/**
 * Interface for local recommendations
 */
export interface LocalRecommendations {
  hiddenGems: {
    name: string;
    description: string;
    bestTimeToVisit: string;
    coordinates: {
      lat: string;
      lng: string;
    };
    tips: string[];
    photos?: string[];
    entryFee?: string;
    crowdLevel?: string;
  }[];
  localEvents: {
    name: string;
    description: string;
    date: string;
    location: string;
    ticketInfo?: string;
    category: string;
    highlights: string[];
    website?: string;
    recurring: boolean;
    suitableFor: string[];
  }[];
  seasonalActivities: {
    activity: string;
    bestSeason: string;
    description: string;
    location: string;
    cost: string;
    requiredEquipment?: string[];
    bookingInfo?: string;
  }[];
  localCuisine: {
    dish: string;
    description: string;
    whereToTry: string[];
    averagePrice: string;
    ingredients: string[];
    history?: string;
    photos?: string[];
  }[];
  transportationTips: string[];
  safetyTips: string[];
  culturalEtiquette: {
    category: string;
    tips: string[];
    importance: 'Essential' | 'Recommended' | 'Optional';
    context: string;
  }[];
  localCustoms: {
    custom: string;
    description: string;
    whenToExperience: string;
    culturalSignificance: string;
  }[];
  shoppingDistricts: {
    name: string;
    description: string;
    specialties: string[];
    openingHours: string;
    priceRange: string;
    bestFor: string[];
    coordinates: {
      lat: string;
      lng: string;
    };
    tips: string[];
  }[];
  nightlifeOptions: {
    name: string;
    type: string;
    description: string;
    bestFor: string[];
    location: string;
    openingHours: string;
    priceRange: string;
    dressCode?: string;
    musicType?: string[];
    coverCharge?: string;
  }[];
  annualEvents: {
    name: string;
    description: string;
    dates: string;
    location: string;
    significance: string;
    highlights: string[];
    ticketInfo?: string;
    tips: string[];
    photos?: string[];
  }[];
  localEtiquette: {
    situation: string;
    doList: string[];
    dontList: string[];
    explanation: string;
  }[];
  usefulResources: {
    apps: {
      name: string;
      purpose: string;
      platform: string;
      url?: string;
      freeOrPaid: string;
      features: string[];
    }[];
    websites: {
      name: string;
      url: string;
      description: string;
      languages: string[];
    }[];
    books: {
      title: string;
      author: string;
      description: string;
      type: string;
      whereToFind?: string;
    }[];
    localServices: {
      name: string;
      type: string;
      description: string;
      contact: string;
    }[];
  };
  localPhrases: {
    phrase: string;
    translation: string;
    pronunciation: string;
    usage: string;
    formalOrInformal: string;
  }[];
}

/**
 * LocalExpertAgent is responsible for providing insider knowledge and local recommendations
 * that typical tourist guides might miss.
 */
export class LocalExpertAgent {
  private model: LanguageModelV1;

  constructor(model: LanguageModelV1) {
    this.model = model;
  }

  /**
   * Get local recommendations for the destination
   */
  async getLocalRecommendations(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
  ): Promise<LocalRecommendations> {
    try {
      console.log(
        'Getting local recommendations for:',
        destinationInfo.destination,
      );

      // Check if duration is null or undefined
      if (
        destinationInfo.duration === null ||
        destinationInfo.duration === undefined
      ) {
        console.log(
          'Duration is null or undefined. Using default local recommendations.',
        );
        // We can still get local recommendations without a duration
      }

      // Execute all tool searches in parallel for better performance
      const [searchResults, locationResults] = await Promise.all([
        this.searchLocalInfo(destinationInfo),
        this.searchLocalLocations(destinationInfo),
      ]);

      // Generate local recommendations using the model
      console.log('🎯 Generating local recommendations with AI model...');
      const { object: recommendations } = await generateObject({
        model: this.model,
        system: `You are a local expert who has lived in the destination for many years.
        Provide insider knowledge and local recommendations that typical tourist guides might miss.
        Focus on authentic experiences that match the user's preferences.
        Include hidden gems, local events, seasonal activities, and cultural insights.
        Be specific and provide detailed information with accurate locations.`,
        prompt: `Provide local expert recommendations for ${destinationInfo.destination}, ${destinationInfo.country}
        that would appeal to a traveler with these preferences:

        ${JSON.stringify(userPreferences, null, 2)}

        Use the following search results to inform your recommendations:
        Web Search Results: ${JSON.stringify(searchResults, null, 2)}
        Location Results: ${JSON.stringify(locationResults, null, 2)}

        Include:
        - Hidden gems that tourists often miss, with tips and practical information
        - Local events happening around the travel dates, with categories and highlights
        - Seasonal activities that are particularly good during the visit, with location and cost information
        - Local cuisine recommendations with specific places to try them, ingredients, and history
        - Transportation tips from a local perspective
        - Safety tips specific to the area
        - Cultural etiquette organized by category (greetings, dining, etc.)
        - Local customs and traditions with cultural significance
        - Shopping districts with local specialties, opening hours, and tips
        - Nightlife options if relevant to the traveler's preferences, with details on atmosphere and prices
        - Annual events that might be of interest for future visits
        - Specific etiquette advice for different situations (dining, religious sites, etc.)
        - Useful resources including apps, websites, books, and local services
        - Common phrases in the local language with pronunciation and usage guidance`,
        schema: z.object({
          hiddenGems: z
            .array(
              z.object({
                name: z.string().describe('Name of the hidden gem'),
                description: z
                  .string()
                  .describe("Description of why it's special"),
                bestTimeToVisit: z
                  .string()
                  .describe('Best time to visit this place'),
                coordinates: z.object({
                  lat: z.string().describe('Latitude coordinate'),
                  lng: z.string().describe('Longitude coordinate'),
                }),
                tips: z
                  .array(z.string())
                  .describe('Tips for visiting this place'),
                entryFee: z
                  .string()
                  .optional()
                  .describe('Entry fee information'),
                crowdLevel: z
                  .string()
                  .optional()
                  .describe('Typical crowd level'),
              }),
            )
            .describe('Hidden gems that tourists often miss'),

          localEvents: z
            .array(
              z.object({
                name: z.string().describe('Name of the event'),
                description: z.string().describe('Description of the event'),
                date: z
                  .string()
                  .describe('Date or period when the event happens'),
                location: z.string().describe('Location of the event'),
                ticketInfo: z
                  .string()
                  .optional()
                  .describe('Ticket information if applicable'),
                category: z.string().describe('Category of the event'),
                highlights: z
                  .array(z.string())
                  .describe('Highlights of the event'),
                recurring: z
                  .boolean()
                  .describe('Whether the event is recurring'),
                suitableFor: z
                  .array(z.string())
                  .describe('Who the event is suitable for'),
              }),
            )
            .describe('Local events happening around the travel dates'),

          seasonalActivities: z
            .array(
              z.object({
                activity: z.string().describe('Seasonal activity'),
                bestSeason: z
                  .string()
                  .describe('Best season for this activity'),
                description: z.string().describe('Description of the activity'),
                location: z.string().describe('Location for this activity'),
                cost: z.string().describe('Cost information'),
                requiredEquipment: z
                  .array(z.string())
                  .optional()
                  .describe('Required equipment'),
                bookingInfo: z
                  .string()
                  .optional()
                  .describe('Booking information'),
              }),
            )
            .describe('Activities that are best during certain seasons'),

          localCuisine: z
            .array(
              z.object({
                dish: z.string().describe('Name of the local dish'),
                description: z.string().describe('Description of the dish'),
                whereToTry: z
                  .array(z.string())
                  .describe('Specific places to try this dish'),
                averagePrice: z.string().describe('Average price of the dish'),
                ingredients: z
                  .array(z.string())
                  .describe('Main ingredients of the dish'),
                history: z.string().optional().describe('History of the dish'),
              }),
            )
            .describe('Local cuisine recommendations'),

          transportationTips: z
            .array(z.string())
            .describe('Transportation tips from a local perspective'),
          safetyTips: z
            .array(z.string())
            .describe('Safety tips specific to the area'),

          culturalEtiquette: z
            .array(
              z.object({
                category: z.string().describe('Category of etiquette'),
                tips: z.array(z.string()).describe('Tips for this category'),
                importance: z
                  .enum(['Essential', 'Recommended', 'Optional'])
                  .describe('Importance level'),
                context: z
                  .string()
                  .describe('Context for these etiquette tips'),
              }),
            )
            .describe('Cultural etiquette to be aware of'),

          localCustoms: z
            .array(
              z.object({
                custom: z.string().describe('Name of the custom'),
                description: z.string().describe('Description of the custom'),
                whenToExperience: z
                  .string()
                  .describe('When to experience this custom'),
                culturalSignificance: z
                  .string()
                  .describe('Cultural significance of this custom'),
              }),
            )
            .describe('Local customs and traditions'),

          shoppingDistricts: z
            .array(
              z.object({
                name: z.string().describe('Name of the shopping district'),
                description: z.string().describe('Description of the district'),
                specialties: z
                  .array(z.string())
                  .describe('What this district is known for'),
                openingHours: z
                  .string()
                  .describe('Opening hours of shops in this district'),
                priceRange: z.string().describe('Price range in this district'),
                bestFor: z
                  .array(z.string())
                  .describe('What this district is best for'),
                coordinates: z.object({
                  lat: z.string().describe('Latitude coordinate'),
                  lng: z.string().describe('Longitude coordinate'),
                }),
                tips: z
                  .array(z.string())
                  .describe('Tips for shopping in this district'),
              }),
            )
            .describe('Shopping districts with local specialties'),

          nightlifeOptions: z
            .array(
              z.object({
                name: z.string().describe('Name of the venue'),
                type: z.string().describe('Type of venue (bar, club, etc.)'),
                description: z.string().describe('Description of the venue'),
                bestFor: z
                  .array(z.string())
                  .describe('What this venue is best for'),
                location: z.string().describe('Location of the venue'),
                openingHours: z.string().describe('Opening hours of the venue'),
                priceRange: z.string().describe('Price range at the venue'),
                dressCode: z
                  .string()
                  .optional()
                  .describe('Dress code if applicable'),
                musicType: z
                  .array(z.string())
                  .optional()
                  .describe('Types of music played'),
                coverCharge: z
                  .string()
                  .optional()
                  .describe('Cover charge information'),
              }),
            )
            .describe('Nightlife options if relevant to the traveler'),

          annualEvents: z
            .array(
              z.object({
                name: z.string().describe('Name of the annual event'),
                description: z.string().describe('Description of the event'),
                dates: z.string().describe('Dates when the event takes place'),
                location: z.string().describe('Location of the event'),
                significance: z
                  .string()
                  .describe('Cultural significance of the event'),
                highlights: z
                  .array(z.string())
                  .describe('Highlights of the event'),
                ticketInfo: z
                  .string()
                  .optional()
                  .describe('Ticket information if applicable'),
                tips: z
                  .array(z.string())
                  .describe('Tips for attending this event'),
              }),
            )
            .describe('Annual events in the destination'),

          localEtiquette: z
            .array(
              z.object({
                situation: z
                  .string()
                  .describe('Situation where etiquette applies'),
                doList: z
                  .array(z.string())
                  .describe('Things to do in this situation'),
                dontList: z
                  .array(z.string())
                  .describe('Things to avoid in this situation'),
                explanation: z
                  .string()
                  .describe('Explanation of the etiquette'),
              }),
            )
            .describe('Specific etiquette for different situations'),

          usefulResources: z
            .object({
              apps: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the app'),
                    purpose: z.string().describe('Purpose of the app'),
                    platform: z
                      .string()
                      .describe('Platforms the app is available on'),
                    url: z
                      .string()
                      .optional()
                      .describe('URL to download the app'),
                    freeOrPaid: z
                      .string()
                      .describe('Whether the app is free or paid'),
                    features: z
                      .array(z.string())
                      .describe('Key features of the app'),
                  }),
                )
                .describe('Useful apps for the destination'),

              websites: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the website'),
                    url: z.string().describe('URL of the website'),
                    description: z
                      .string()
                      .describe('Description of the website'),
                    languages: z
                      .array(z.string())
                      .describe('Languages available on the website'),
                  }),
                )
                .describe('Useful websites for the destination'),

              books: z
                .array(
                  z.object({
                    title: z.string().describe('Title of the book'),
                    author: z.string().describe('Author of the book'),
                    description: z.string().describe('Description of the book'),
                    type: z.string().describe('Type of book'),
                    whereToFind: z
                      .string()
                      .optional()
                      .describe('Where to find the book'),
                  }),
                )
                .describe('Useful books about the destination'),

              localServices: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the service'),
                    type: z.string().describe('Type of service'),
                    description: z
                      .string()
                      .describe('Description of the service'),
                    contact: z.string().describe('Contact information'),
                  }),
                )
                .describe('Useful local services'),
            })
            .describe('Useful resources for the destination'),

          localPhrases: z
            .array(
              z.object({
                phrase: z.string().describe('English phrase'),
                translation: z
                  .string()
                  .describe('Translation in local language'),
                pronunciation: z.string().describe('Pronunciation guide'),
                usage: z.string().describe('When to use this phrase'),
                formalOrInformal: z
                  .string()
                  .describe('Whether the phrase is formal or informal'),
              }),
            )
            .describe('Useful phrases in the local language'),
        }),
        temperature: 0.7,
      });

      console.log('✅ Local recommendations generated successfully:');
      console.log('- Hidden Gems:', recommendations.hiddenGems?.length || 0);
      console.log('- Local Events:', recommendations.localEvents?.length || 0);
      console.log(
        '- Seasonal Activities:',
        recommendations.seasonalActivities?.length || 0,
      );
      console.log(
        '- Nightlife Options:',
        recommendations.nightlifeOptions?.length || 0,
      );
      console.log(
        '- Annual Events:',
        recommendations.annualEvents?.length || 0,
      );

      return recommendations;
    } catch (error) {
      console.error('Error getting local recommendations:', error);

      // Return fallback recommendations if generation fails
      return this.getFallbackRecommendations(destinationInfo);
    }
  }

  /**
   * Search for local information about the destination
   */
  private async searchLocalInfo(
    destinationInfo: DestinationInfo,
  ): Promise<any> {
    try {
      // Create a mock session and dataStream for the web_search tool
      const mockSession = {
        user: { id: 'system', type: 'guest' as const },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };
      // Create a mock DataStreamWriter
      const mockDataStream: DataStreamWriter = {
        writeMessageAnnotation: () => {},
        write: () => '',
        writeData: () => '',
        writeSource: () => '',
        merge: () => '',
        onError: () => '',
      };

      // Use the web_search tool to get local information
      const searchTool = web_search({
        session: mockSession,
        dataStream: mockDataStream,
      });

      // Create search queries for different aspects of local information
      const queries = [
        `hidden gems in ${destinationInfo.destination} ${destinationInfo.country} local secrets`,
        `local events in ${destinationInfo.destination} ${destinationInfo.country} this month`,
        `${destinationInfo.destination} ${destinationInfo.country} local cuisine best restaurants`,
        `${destinationInfo.destination} ${destinationInfo.country} cultural etiquette customs`,
        `${destinationInfo.destination} ${destinationInfo.country} local transportation tips`,
      ];

      // Execute all search queries in parallel
      const searchPromises = queries.map((query) =>
        // Use type assertion to avoid TypeScript error
        (searchTool.execute as any)({
          queries: [query],
          maxResults: [3],
          topics: ['general'],
        }),
      );

      const results = await Promise.all(searchPromises);
      return results;
    } catch (error) {
      console.error('Error searching local information:', error);
      return { results: [] };
    }
  }

  /**
   * Get fallback recommendations if generation fails
   */
  private getFallbackRecommendations(
    destinationInfo: DestinationInfo,
  ): LocalRecommendations {
    return {
      hiddenGems: [
        {
          name: `Local Park in ${destinationInfo.destination}`,
          description:
            'A beautiful park where locals like to relax away from tourist crowds.',
          bestTimeToVisit: 'Early morning or late afternoon',
          coordinates: {
            lat: destinationInfo.coordinates.lat,
            lng: destinationInfo.coordinates.lng,
          },
          tips: [
            'Bring a book or picnic',
            'Check out the central fountain',
            'Visit the small café at the north entrance',
          ],
          entryFee: 'Free',
          crowdLevel: 'Low to moderate, busier on weekends',
        },
      ],
      localEvents: [
        {
          name: 'Local Market',
          description:
            'Weekly market where locals sell fresh produce and crafts.',
          date: 'Every Saturday morning',
          location: 'City center',
          category: 'Market',
          highlights: [
            'Fresh local produce',
            'Artisanal crafts',
            'Street food',
          ],
          recurring: true,
          suitableFor: ['Families', 'Food enthusiasts', 'Souvenir shoppers'],
        },
      ],
      seasonalActivities: [
        {
          activity: 'City Walking Tour',
          bestSeason: 'Spring and Fall',
          description: 'Explore the city on foot when the weather is pleasant.',
          location: 'Starting from Main Square',
          cost: 'Free - €20 depending on tour type',
          requiredEquipment: ['Comfortable walking shoes', 'Water bottle'],
          bookingInfo: 'Some tours require advance booking, others are walk-up',
        },
      ],
      localCuisine: [
        {
          dish: 'Local Specialty',
          description: 'A traditional dish from the region.',
          whereToTry: ['Local Restaurant', 'City Market'],
          averagePrice: '€10-15',
          ingredients: [
            'Local ingredient 1',
            'Local ingredient 2',
            'Herbs and spices',
          ],
          history:
            'This dish dates back to the 18th century and was traditionally prepared for celebrations.',
        },
      ],
      transportationTips: [
        {
          category: 'Public Transport',
          tip: 'Public transportation is generally reliable and efficient',
          cost: 'Day passes €5-15, single tickets €1-3',
          timeToAvoid: 'Rush hours 7-9 AM and 5-7 PM',
          insiderSecret: 'Buy weekly passes on Sundays for best value',
        },
        {
          category: 'Walking',
          tip: 'City center attractions are within walking distance',
          cost: 'Free',
          timeToAvoid: 'Midday heat in summer',
          insiderSecret: 'Use pedestrian shortcuts through shopping centers',
        },
        {
          category: 'Taxi',
          tip: 'Taxis are readily available but check meter is running',
          cost: '€2-3 base fare + €1-2 per km',
          timeToAvoid: 'Late night surcharges after midnight',
          insiderSecret: 'Use ride-sharing apps for better prices',
        },
      ],
      safetyTips: [
        {
          category: 'General Safety',
          tip: 'The city is generally safe, but stay alert in crowded tourist areas',
          timeOfDay: 'Always',
          location: 'Tourist areas and crowded places',
          severity: 'Important',
          whatToAvoid: 'Displaying expensive items openly',
          emergencyContacts: ['Police: 112', 'Tourist Police: ******-HELP'],
        },
        {
          category: 'Valuables',
          tip: 'Keep your valuables secure and use hotel safes',
          timeOfDay: 'Always',
          location: 'Everywhere, especially tourist areas',
          severity: 'Critical',
          whatToAvoid:
            'Leaving bags unattended, carrying large amounts of cash',
          emergencyContacts: ['Police: 112', 'Embassy: ******-EMBASSY'],
        },
        {
          category: 'Night Safety',
          tip: 'Be extra aware of your surroundings after dark',
          timeOfDay: 'Night',
          location: 'All areas, especially quiet streets',
          severity: 'Important',
          whatToAvoid: 'Walking alone in poorly lit areas',
          emergencyContacts: ['Police: 112', 'Taxi: ******-TAXI'],
        },
        {
          category: 'Emergency Preparedness',
          tip: 'Save emergency numbers in your phone and keep them accessible',
          timeOfDay: 'Always',
          location: 'Everywhere',
          severity: 'Essential',
          whatToAvoid: 'Relying only on internet for emergency contacts',
          emergencyContacts: [
            'Emergency: 112',
            'Hospital: ******-HOSPITAL',
            'Tourist Hotline: ******-TOURIST',
          ],
        },
      ],
      culturalEtiquette: [
        {
          category: 'Greetings',
          situation: 'Meeting people for the first time',
          doList: [
            'Greet with a smile and make eye contact',
            'Offer a firm handshake',
            'Use formal titles until invited to use first names',
          ],
          dontList: [
            "Don't be overly familiar immediately",
            "Don't interrupt conversations",
            "Don't ignore social hierarchy",
          ],
          tippingGuideline: {
            expected: false,
            amount: 'Not applicable',
            method: 'Not applicable',
            timing: 'Not applicable',
          },
          dressCode: {
            men: 'Smart casual or business casual',
            women: 'Smart casual, modest necklines',
            children: 'Clean, neat clothing',
            forbidden: [
              'Overly casual wear in formal settings',
              'Revealing clothing',
            ],
          },
          phrases: [
            {
              phrase: 'Nice to meet you',
              pronunciation: '[Local pronunciation]',
              when: 'First meetings',
            },
            {
              phrase: 'How are you?',
              pronunciation: '[Local pronunciation]',
              when: 'Casual greetings',
            },
          ],
          importance: 'Essential',
          consequences:
            'Poor first impressions can affect business and social relationships',
          context:
            'First impressions are crucial in local culture and business',
          insiderTips: [
            'Wait for the other person to extend their hand first',
            'Maintain eye contact during handshakes',
            'Remember names and use them in conversation',
          ],
        },
        {
          category: 'Dining',
          situation: 'Eating at restaurants and social meals',
          doList: [
            'Wait to be seated by staff',
            'Keep hands visible on the table',
            'Try to finish most of your meal',
            'Wait for everyone to be served before eating',
          ],
          dontList: [
            "Don't start eating before others",
            "Don't be overly loud",
            "Don't rush through meals",
            "Don't expect free tap water everywhere",
          ],
          tippingGuideline: {
            expected: true,
            amount: '10-15% for good service',
            method: 'Cash preferred, can add to card payment',
            timing: 'At the end of the meal',
          },
          dressCode: {
            men: 'Smart casual for dinner, casual for lunch',
            women: 'Smart casual, avoid overly revealing clothing',
            children: 'Clean, neat clothing',
            forbidden: [
              'Beachwear',
              'Athletic wear',
              'Flip-flops in upscale restaurants',
            ],
          },
          phrases: [
            {
              phrase: 'The bill, please',
              pronunciation: '[Local pronunciation]',
              when: 'When ready to pay',
            },
            {
              phrase: 'This is delicious',
              pronunciation: '[Local pronunciation]',
              when: 'Complimenting the food',
            },
          ],
          importance: 'Essential',
          consequences: 'Poor dining etiquette can be seen as disrespectful',
          context:
            'Dining is a social experience meant to be enjoyed leisurely',
          insiderTips: [
            'Bread is often complimentary but not always',
            'Service can be slower - this is normal, not poor service',
            'Splitting bills is common among friends',
          ],
        },
      ],
      localCustoms: [
        {
          custom: 'Afternoon Break',
          description: 'Many shops close for a few hours in the afternoon',
          whenToExperience: 'Typically between 13:00-16:00',
          culturalSignificance:
            'Traditional practice that allows for family lunch and rest',
        },
        {
          custom: 'Local Festival',
          description: 'Annual celebration with parades and special foods',
          whenToExperience: 'Usually held in summer',
          culturalSignificance:
            "Celebrates the city's patron saint and cultural heritage",
        },
      ],
      shoppingDistricts: [
        {
          name: 'Main Shopping Street',
          description:
            'The main shopping area with a mix of local and international stores.',
          specialties: ['Local crafts', 'Souvenirs', 'Fashion'],
          openingHours: '10:00-19:00 Monday-Saturday, limited hours Sunday',
          priceRange: 'Moderate to expensive',
          bestFor: ['Souvenir shopping', 'Fashion', 'Gift items'],
          coordinates: {
            lat: destinationInfo.coordinates.lat,
            lng: destinationInfo.coordinates.lng,
          },
          tips: [
            'Smaller shops may close for lunch',
            'Better prices at shops further from the main square',
            "Don't be afraid to negotiate at craft markets",
          ],
        },
      ],
      nightlifeOptions: [
        {
          name: 'Local Bar',
          type: 'Bar',
          description: 'A popular spot for locals to gather in the evening.',
          bestFor: ['Casual drinks', 'Meeting locals'],
          location: 'Old Town',
          openingHours: '18:00-02:00, Tuesday-Sunday',
          priceRange: 'Moderate',
          musicType: ['Local music', 'International hits'],
          dressCode: 'Casual',
        },
      ],
      annualEvents: [
        {
          name: `${destinationInfo.destination} Summer Festival`,
          description: 'Annual cultural festival with music, dance, and food',
          dates: 'Last weekend of July',
          location: 'Throughout the city center',
          significance:
            'Celebrates local culture and attracts visitors from around the region',
          highlights: [
            'Live music performances',
            'Traditional dance',
            'Food stalls',
            'Craft market',
          ],
          ticketInfo:
            'Most events are free, some special performances require tickets',
          tips: [
            'Book accommodation well in advance',
            'Check the official program for event times',
            'Some venues have limited capacity, arrive early',
          ],
        },
        {
          name: 'Winter Holiday Market',
          description:
            'Traditional holiday market with local crafts and seasonal foods',
          dates: 'December 1-24',
          location: 'Main Square',
          significance:
            'Traditional winter celebration with roots in medieval times',
          highlights: [
            'Handcrafted gifts',
            'Seasonal treats',
            'Mulled wine',
            "Children's activities",
          ],
          tips: [
            'Evening visits offer the best atmosphere with lights',
            'Weekdays are less crowded than weekends',
            'Bring cash as not all vendors accept cards',
          ],
        },
      ],
      localEtiquette: [
        {
          situation: 'Dining in restaurants',
          doList: [
            'Wait to be seated',
            'Keep hands visible on the table',
            'Try to finish everything on your plate',
          ],
          dontList: [
            "Don't be overly loud",
            "Don't rush through meals",
            "Don't expect tap water automatically",
          ],
          explanation:
            'Dining is a social experience meant to be enjoyed at a leisurely pace',
        },
        {
          situation: 'Visiting religious sites',
          doList: [
            'Dress modestly covering shoulders and knees',
            'Speak quietly',
            'Follow posted rules',
          ],
          dontList: [
            "Don't take photos where prohibited",
            "Don't enter during services unless participating",
            "Don't touch religious artifacts",
          ],
          explanation:
            'Religious sites are places of worship first and tourist attractions second',
        },
      ],
      usefulResources: {
        apps: [
          {
            name: 'Local Transport App',
            purpose: 'Navigate public transportation',
            platform: 'iOS, Android',
            freeOrPaid: 'Free',
            features: [
              'Real-time schedules',
              'Route planning',
              'Ticket purchasing',
            ],
          },
          {
            name: 'City Guide App',
            purpose: 'Comprehensive city information',
            platform: 'iOS, Android',
            url: `https://cityguide.${destinationInfo.destination.toLowerCase().replace(/\s+/g, '')}.com`,
            freeOrPaid: 'Free with in-app purchases',
            features: [
              'Offline maps',
              'Attraction information',
              'Suggested itineraries',
            ],
          },
        ],
        websites: [
          {
            name: 'Official Tourism Website',
            url: `https://visit${destinationInfo.destination.toLowerCase().replace(/\s+/g, '')}.com`,
            description:
              'Official tourism information with events calendar and practical tips',
            languages: ['English', 'Local language', 'French', 'German'],
          },
          {
            name: 'Local Events Calendar',
            url: `https://events.${destinationInfo.destination.toLowerCase().replace(/\s+/g, '')}.com`,
            description:
              'Comprehensive listing of cultural events, exhibitions, and performances',
            languages: ['English', 'Local language'],
          },
        ],
        books: [
          {
            title: `${destinationInfo.destination} - A Cultural History`,
            author: 'Local Historian',
            description:
              "In-depth exploration of the city's cultural heritage and development",
            type: 'History',
            whereToFind: 'Major bookstores and museum shops',
          },
          {
            title: `Walking Tours of ${destinationInfo.destination}`,
            author: 'Travel Writer',
            description:
              'Self-guided walking tours with historical context and hidden gems',
            type: 'Guidebook',
            whereToFind: 'Bookstores, tourist information centers, and online',
          },
        ],
        localServices: [
          {
            name: 'Tourist Information Center',
            type: 'Information Service',
            description:
              'Official information point with maps, brochures, and local advice',
            contact: 'Main Square, open daily 9:00-18:00',
          },
          {
            name: 'Guided Tour Association',
            type: 'Tour Service',
            description:
              'Organization offering various themed tours with certified local guides',
            contact: 'www.localguidedtours.com, +123 456 7890',
          },
        ],
      },
      localPhrases: [
        {
          phrase: 'Hello',
          translation: '[Local translation]',
          pronunciation: '[Simplified pronunciation]',
          usage: 'Greeting people throughout the day',
          formalOrInformal: 'Both',
        },
        {
          phrase: 'Thank you',
          translation: '[Local translation]',
          pronunciation: '[Simplified pronunciation]',
          usage: 'Expressing gratitude for services or help',
          formalOrInformal: 'Both',
        },
        {
          phrase: 'Excuse me',
          translation: '[Local translation]',
          pronunciation: '[Simplified pronunciation]',
          usage: 'Getting attention or apologizing for minor inconveniences',
          formalOrInformal: 'Formal',
        },
        {
          phrase: 'Cheers',
          translation: '[Local translation]',
          pronunciation: '[Simplified pronunciation]',
          usage: 'Toasting before drinking',
          formalOrInformal: 'Informal',
        },
      ],
    } as unknown as LocalRecommendations;
  }

  /**
   * Search for local locations using map search
   */
  private async searchLocalLocations(
    destinationInfo: DestinationInfo,
  ): Promise<any> {
    try {
      // Import map_search tool
      const { map_search } = await import('@/lib/ai/tools/map-search');

      // Create a mock session and dataStream for the map_search tool
      const mockSession = {
        user: { id: 'system', type: 'guest' as const },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };
      const mockDataStream: DataStreamWriter = {
        writeMessageAnnotation: () => {},
        write: () => '',
        writeData: () => '',
        writeSource: () => '',
        merge: () => '',
        onError: () => '',
      };

      const mapSearchTool = map_search({
        session: mockSession,
        dataStream: mockDataStream,
      });

      // Create search queries for local locations
      const locationQueries = [
        `hidden gems ${destinationInfo.destination} ${destinationInfo.country}`,
        `local markets ${destinationInfo.destination}`,
        `shopping districts ${destinationInfo.destination}`,
        `nightlife ${destinationInfo.destination}`,
      ];

      // Execute all location searches in parallel
      const locationPromises = locationQueries.map((query) =>
        (mapSearchTool.execute as any)({
          query,
          includeDetails: true,
        }),
      );

      const results = await Promise.all(locationPromises);
      return results;
    } catch (error) {
      console.error('Error searching local locations:', error);
      return { results: [] };
    }
  }

  /**
   * Generate curated images for local recommendations (avoiding Serper issues)
   */
  private async searchLocalImages(
    destinationInfo: DestinationInfo,
  ): Promise<any> {
    try {
      console.log(
        `🖼️ Generating curated images for ${destinationInfo.destination}...`,
      );

      // Generate destination-specific Unsplash images
      const destination = encodeURIComponent(destinationInfo.destination);
      const country = encodeURIComponent(destinationInfo.country);

      const curatedImages = [
        `https://source.unsplash.com/400x200/?${destination},${country},attraction`,
        `https://source.unsplash.com/400x200/?${destination},${country},landmark`,
        `https://source.unsplash.com/400x200/?${destination},${country},tourist`,
        `https://source.unsplash.com/400x200/?${destination},${country},architecture`,
        `https://source.unsplash.com/400x200/?${destination},${country},culture`,
      ];

      console.log(
        `✅ Generated ${curatedImages.length} curated images for ${destinationInfo.destination}`,
      );

      return {
        images: curatedImages.map((url) => ({ imageUrl: url })),
        queries: [`${destinationInfo.destination} attractions`],
      };
    } catch (error) {
      console.error('Error generating curated images:', error);
      return { images: [] };
    }
  }
}
