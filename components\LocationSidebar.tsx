'use client';

import React, { useState } from 'react';
import type { Place } from '@/lib/types';
import { PlusIcon, CrossIcon } from './icons';

interface LocationSidebarProps {
  places: Place[];
  selectedPlaceId?: string | null;
  onLocationClick?: (place: Place) => void;
}

const LocationSidebar: React.FC<LocationSidebarProps> = ({
  places,
  selectedPlaceId,
  onLocationClick,
}) => {
  const [showMore, setShowMore] = useState(false);
  // only show the first 5 places
  const displayPlaces = places.slice(0, showMore ? places.length : 3);

  return (
    <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-3 sm:p-4 mt-4">
      <div className="flex items-center">
        <h2 className="text-base sm:text-lg font-semibold flex-grow text-black dark:text-white">
          Location Details
        </h2>
        {places.length > 3 && (
          <div className="flex justify-center ml-2">
            <button
              type="button"
              className="text-black dark:text-white focus:outline-none p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded transition-colors"
              onClick={() => setShowMore(!showMore)}
              aria-label={
                showMore ? 'Show less locations' : 'Show more locations'
              }
            >
              {showMore ? <CrossIcon /> : <PlusIcon />}
            </button>
          </div>
        )}
      </div>
      <div
        className={`space-y-3 sm:space-y-4 transition-all duration-500 ${showMore ? 'max-h-[5000px]' : 'max-h-[300px]'} overflow-hidden`}
      >
        {displayPlaces.map((place: Place) => {
          const isSelected = selectedPlaceId === place.cid;

          return (
            <div
              key={place.cid}
              className={`
                rounded-lg p-3 sm:p-4 cursor-pointer transition-all duration-200
                ${
                  isSelected
                    ? 'bg-blue-100 dark:bg-blue-900/30 border-2 border-blue-500 dark:border-blue-400 shadow-md'
                    : 'bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 border-2 border-transparent'
                }
              `}
              onClick={() => onLocationClick?.(place)}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  onLocationClick?.(place);
                }
              }}
              aria-label={`View ${place.title} on map`}
            >
              <h3 className="text-base sm:text-lg font-semibold mb-2 text-black dark:text-white leading-tight">
                {place.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 text-xs sm:text-sm mb-2 leading-relaxed">
                {place.address}
              </p>
              <div className="flex items-center mb-2">
                <span className="text-gray-600 dark:text-gray-400 text-xs sm:text-sm mr-1">
                  Rating:
                </span>
                <div className="flex items-center">
                  {[...Array(5)].map((_, index) => (
                    <svg
                      key={`star-${place.cid}-${index}`}
                      className={`w-3 h-3 sm:w-4 sm:h-4 ${
                        index < Math.floor(place.rating)
                          ? 'text-yellow-400'
                          : 'text-gray-300 dark:text-gray-500'
                      }`}
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 15.585l-5.293 2.776.1-5.867L.416 8.222l5.875-.855L10 2.415l3.709 4.952 5.875.855-4.391 4.272.1 5.867L10 15.585z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ))}
                  <span className="ml-1 text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                    {place.rating.toFixed(1)}
                  </span>
                </div>
              </div>
              <p className="text-gray-600 dark:text-gray-400 text-xs sm:text-sm mb-2">
                Category: {place.category}
              </p>
              {place.phoneNumber && (
                <div className="mb-2">
                  <a
                    href={`tel:${place.phoneNumber}`}
                    className="inline-flex items-center text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 text-xs sm:text-sm font-medium py-1 px-2 rounded bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                  >
                    <span className="mr-1">📞</span>
                    {place.phoneNumber}
                  </a>
                </div>
              )}
              {place.website && (
                <div className="mb-2">
                  <a
                    href={place.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 text-xs sm:text-sm font-medium py-1 px-2 rounded bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                  >
                    <span className="mr-1">🌐</span>
                    <span className="truncate max-w-[150px] sm:max-w-[200px]">
                      Visit Website
                    </span>
                  </a>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default LocationSidebar;
