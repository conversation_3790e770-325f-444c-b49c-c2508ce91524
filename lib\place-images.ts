import type { Place, PlaceImage } from './types';

/**
 * Enrichit les lieux avec des images supplémentaires si nécessaire
 */
export async function enrichPlacesWithImages(
  places: Place[],
): Promise<Place[]> {
  const enrichedPlaces = await Promise.all(
    places.map(async (place) => {
      // Si le lieu a déjà des images, on les garde
      if (place.images && place.images.length > 0) {
        return place;
      }

      // Sinon, on essaie de récupérer des images
      const images = await fetchPlaceImages(place);
      return {
        ...place,
        images: images.length > 0 ? images : undefined,
      };
    }),
  );

  return enrichedPlaces;
}

/**
 * Récupère des images pour un lieu spécifique
 */
async function fetchPlaceImages(place: Place): Promise<PlaceImage[]> {
  const images: PlaceImage[] = [];

  try {
    // Essayer d'abord avec Google Places API si disponible
    const googleImages = await fetchGooglePlacesImages(place);
    images.push(...googleImages);

    // Si pas assez d'images, utiliser des sources alternatives
    if (images.length < 3) {
      const fallbackImages = await fetchFallbackImages(place);
      images.push(...fallbackImages);
    }

    // Limiter à 5 images maximum
    return images.slice(0, 5);
  } catch (error) {
    console.error(`Error fetching images for ${place.title}:`, error);
    return [];
  }
}

/**
 * Récupère des images via Google Places API (si disponible)
 */
async function fetchGooglePlacesImages(place: Place): Promise<PlaceImage[]> {
  // Cette fonction pourrait utiliser Google Places API
  // Pour l'instant, on retourne un tableau vide
  return [];
}

/**
 * Récupère des images de fallback basées sur le type de lieu
 */
async function fetchFallbackImages(place: Place): Promise<PlaceImage[]> {
  const images: PlaceImage[] = [];

  // Générer des URLs d'images basées sur le type de lieu et le nom
  const searchTerms = generateSearchTerms(place);

  for (const term of searchTerms) {
    const imageUrl = generateUnsplashUrl(term, 800, 600);
    const thumbnailUrl = generateUnsplashUrl(term, 300, 200);

    images.push({
      url: imageUrl,
      description: `${place.title} - ${term}`,
      thumbnail: thumbnailUrl,
      width: 800,
      height: 600,
    });
  }

  return images;
}

/**
 * Génère des termes de recherche basés sur le lieu
 */
function generateSearchTerms(place: Place): string[] {
  const terms: string[] = [];
  const category = place.category.toLowerCase();
  const title = place.title.toLowerCase();

  // Termes basés sur la catégorie
  if (category.includes('restaurant') || category.includes('food')) {
    terms.push('restaurant interior', 'food dining', 'cuisine');
  } else if (category.includes('hotel') || category.includes('accommodation')) {
    terms.push('hotel lobby', 'hotel room', 'accommodation');
  } else if (category.includes('museum') || category.includes('attraction')) {
    terms.push('museum interior', 'tourist attraction', 'cultural site');
  } else if (category.includes('park') || category.includes('garden')) {
    terms.push('park landscape', 'garden nature', 'outdoor space');
  } else if (category.includes('shop') || category.includes('store')) {
    terms.push('shop interior', 'retail store', 'shopping');
  } else if (category.includes('church') || category.includes('cathedral')) {
    terms.push(
      'church interior',
      'cathedral architecture',
      'religious building',
    );
  } else {
    terms.push('building exterior', 'architecture', 'landmark');
  }

  // Ajouter le nom du lieu si c'est un monument connu
  if (title.includes('eiffel') || title.includes('tower')) {
    terms.push('eiffel tower');
  } else if (title.includes('louvre')) {
    terms.push('louvre museum');
  } else if (title.includes('arc') && title.includes('triomphe')) {
    terms.push('arc de triomphe');
  }

  return terms.slice(0, 3); // Limiter à 3 termes
}

/**
 * Génère une URL d'image fiable avec des paramètres spécifiques
 */
function generateImageUrl(
  searchTerm: string,
  width: number,
  height: number,
): string {
  // Utiliser Picsum Photos comme source plus fiable
  const imageId = getImageIdFromTerm(searchTerm);
  return `https://picsum.photos/${width}/${height}?random=${imageId}`;
}

/**
 * Génère un ID d'image basé sur le terme de recherche pour la cohérence
 */
function getImageIdFromTerm(searchTerm: string): number {
  // Créer un hash simple du terme pour avoir des images cohérentes
  let hash = 0;
  for (let i = 0; i < searchTerm.length; i++) {
    const char = searchTerm.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  // Retourner un nombre entre 1 et 1000 (range d'images Picsum)
  return Math.abs(hash % 1000) + 1;
}

/**
 * Valide qu'une URL d'image est accessible
 */
export async function validateImageUrl(url: string): Promise<boolean> {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return (
      response.ok && response.headers.get('content-type')?.startsWith('image/')
    );
  } catch {
    return false;
  }
}

/**
 * Optimise les images pour différentes tailles d'écran
 */
export function getOptimizedImageUrl(
  image: PlaceImage,
  size: 'thumbnail' | 'medium' | 'large',
): string {
  if (size === 'thumbnail' && image.thumbnail) {
    return image.thumbnail;
  }

  // Si l'URL est d'Unsplash, on peut la modifier pour la taille
  if (image.url.includes('source.unsplash.com')) {
    const baseUrl = image.url.split('?')[0];
    const searchTerm = image.url.split('?')[1] || '';

    switch (size) {
      case 'thumbnail':
        return `${baseUrl.replace(/\d+x\d+/, '150x150')}?${searchTerm}`;
      case 'medium':
        return `${baseUrl.replace(/\d+x\d+/, '400x300')}?${searchTerm}`;
      case 'large':
        return `${baseUrl.replace(/\d+x\d+/, '800x600')}?${searchTerm}`;
      default:
        return image.url;
    }
  }

  return image.url;
}

/**
 * Ajoute des images de démonstration pour les tests
 */
export function addDemoImages(places: Place[]): Place[] {
  return places.map((place) => {
    if (place.images && place.images.length > 0) {
      return place; // Garder les images existantes
    }

    // Ajouter des images de démo basées sur la catégorie
    const demoImages = generateFallbackImages(place);
    return {
      ...place,
      images: demoImages,
    };
  });
}

/**
 * Génère des images de fallback synchrones pour les démos
 */
function generateFallbackImages(place: Place): PlaceImage[] {
  const searchTerms = generateSearchTerms(place);

  return searchTerms.map((term, index) => ({
    url: generateUnsplashUrl(term, 800, 600),
    description: `${place.title} - ${term}`,
    thumbnail: generateUnsplashUrl(term, 300, 200),
    width: 800,
    height: 600,
  }));
}
