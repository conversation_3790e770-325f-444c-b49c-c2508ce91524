declare module 'mem0ai' {
  export interface MemoryOptions {
    user_id: string;
    org_id: string;
    project_id: string;
    metadata?: Record<string, any>;
    categories?: string[];
  }

  export interface MemorySearchOptions {
    user_id?: string;
    org_id?: string;
    project_id?: string;
    limit?: number;
    filters?: Record<string, any>;
    api_version?: string;
  }

  export interface MemoryAddResponse {
    id: string;
    data: {
      memory: string;
    };
    event: 'ADD';
  }

  export interface MemorySearchResponse {
    id: string;
    memory: string;
    user_id: string;
    metadata: Record<string, any> | null;
    categories: string[];
    immutable: boolean;
    created_at: string;
    updated_at: string;
    message: string | null;
  }

  // Ajout d'une interface d'array pour les réponses de recherche
  export interface MemorySearchResponseArray
    extends Array<MemorySearchResponse> {
    length: number;
    sort(
      compareFn?: (a: MemorySearchResponse, b: MemorySearchResponse) => number,
    ): this;
    find(
      predicate: (value: MemorySearchResponse) => boolean,
    ): MemorySearchResponse | undefined;
    map<U>(callbackfn: (value: MemorySearchResponse) => U): U[];
    [index: number]: MemorySearchResponse;
  }

  export class MemoryClient {
    constructor(options: { apiKey: string });

    add(memory: string, options: MemoryOptions): Promise<MemoryAddResponse[]>;

    search(
      query: string,
      options: Partial<MemorySearchOptions>,
    ): Promise<MemorySearchResponseArray>;
  }
}
