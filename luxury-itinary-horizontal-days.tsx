'use client';

import { useState } from 'react';
import {
  Clock,
  MapPin,
  Star,
  Phone,
  Utensils,
  Camera,
  TreePine,
  ChevronDown,
  ChevronUp,
  Minus,
  Calendar,
  Building,
  Eye,
  Mountain,
  Waves,
  X,
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

const itineraryData = [
  {
    day: 1,
    date: 'Premier Jour',
    activities: [
      {
        time: '9:00 AM',
        title: 'Temple Senso-ji & Marché <PERSON>-dori',
        location: '2 Chome-3-1 Asakusa, Taito City, Tokyo 111-0032, Japan',
        description:
          "Explorez le plus ancien temple de Tokyo, Senso-ji, et son marché vibrant Nakamise-dori. Découvrez l'atmosphère traditionnelle et achetez des souvenirs.",
        duration: '2 heures',
        type: 'culture',
        icon: TreePine,
        rating: '4.7/5',
        reviews: '(2145 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3842-0181',
      },
      {
        time: '11:00 AM',
        title: "Ruelles Secrètes & Joyaux Cachés d'Asakusa",
        location: 'Asakusa, Taito City, Tokyo',
        description:
          "Aventurez-vous au-delà des rues principales pour découvrir les charmantes ruelles d'Asakusa, les boutiques d'artisanat traditionnel et les restaurants locaux.",
        duration: '1.5 heures',
        type: 'exploration',
        icon: Camera,
        rating: '4.5/5',
        reviews: '(987 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3844-7788',
      },
      {
        time: '12:30 PM',
        title: 'Déjeuner chez Daikokuya Tempura',
        location: '1-38-13 Asakusa, Taito-ku, Tokyo',
        description:
          'Savourez un délicieux déjeuner tempura chez Daikokuya Tempura, un restaurant local populaire connu pour ses portions généreuses et ses plats savoureux.',
        duration: '1 heure',
        type: 'dining',
        icon: Utensils,
        rating: '4.6/5',
        reviews: '(1876 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3844-1111',
      },
      {
        time: '2:30 PM',
        title: 'Tokyo Skytree',
        location: '1 Chome-1-2 Oshiage, Sumida City, Tokyo 131-0045, Japan',
        description:
          "Montez à Tokyo Skytree pour des vues panoramiques sur la ville. Profitez des ponts d'observation et explorez le complexe commercial Solamachi.",
        duration: '2 heures',
        type: 'sightseeing',
        icon: Building,
        rating: '4.4/5',
        reviews: '(3012 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-5302-3470',
      },
      {
        time: '8:00 PM',
        title: 'Dîner chez Ginza Kyubey (Sushi)',
        location: '7-6, Ginza 8-chome, Chuo-ku, Tokyo, 104-0061',
        description:
          'Dînez chez Ginza Kyubey, un restaurant de sushi renommé à Ginza, connu pour ses sushis exquis et son atmosphère traditionnelle.',
        duration: '2 heures',
        type: 'fine-dining',
        icon: Utensils,
        rating: '4.8/5',
        reviews: '(2654 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3571-6523',
      },
    ],
  },
  {
    day: 2,
    date: 'Deuxième Jour',
    activities: [
      {
        time: '9:00 AM',
        title: 'Jardin Est du Palais Impérial',
        location: '1-1 Chiyoda, Chiyoda City, Tokyo 100-8111, Japan',
        description:
          "Explorez le jardin Est du Palais Impérial, l'ancien site du château d'Edo. Promenez-vous dans les terrains historiques et profitez de l'atmosphère sereine.",
        duration: '2 heures',
        type: 'culture',
        icon: TreePine,
        rating: '4.6/5',
        reviews: '(1845 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3213-1111',
      },
      {
        time: '11:00 AM',
        title: 'District de Ginza',
        location: 'Ginza, Chuo City, Tokyo',
        description:
          'Promenez-vous dans le quartier huppé de Ginza, connu pour ses boutiques de luxe, ses grands magasins et ses cafés élégants.',
        duration: '1.5 heures',
        type: 'shopping',
        icon: Building,
        rating: '4.4/5',
        reviews: '(1234 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3572-5755',
      },
      {
        time: '12:30 PM',
        title: 'Déjeuner chez Ginza Bairin (Tonkatsu)',
        location: '7-7-10 Ginza, Chuo-ku, Tokyo',
        description:
          'Savourez un déjeuner chez Ginza Bairin, un restaurant à Ginza spécialisé dans le Tonkatsu.',
        duration: '1 heure',
        type: 'dining',
        icon: Utensils,
        rating: '4.3/5',
        reviews: '(1254 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3571-0342',
      },
      {
        time: '6:00 PM',
        title: 'Observatoire Shibuya Sky',
        location: '2 Chome-24-12 スクランブルスクエア, Shibuya, Tokyo 150-6145',
        description:
          "Visitez Shibuya Sky pour des vues panoramiques sur la ville. Profitez des ponts d'observation et explorez le Shibuya Scramble Square.",
        duration: '2 heures',
        type: 'sightseeing',
        icon: Eye,
        rating: '4.5/5',
        reviews: '(2876 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-6450-6910',
      },
      {
        time: '8:00 PM',
        title: 'Dîner chez Han no Daidokoro Bettei (Wagyu)',
        location: '1 Chome-6-1 Shibuya, Shibuya City, Tokyo 150-0002, Japan',
        description:
          'Dînez chez Han no Daidokoro Bettei à Shibuya, connu pour son bœuf Wagyu exquis.',
        duration: '2 heures',
        type: 'fine-dining',
        icon: Utensils,
        rating: '4.7/5',
        reviews: '(1987 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-5428-4129',
      },
    ],
  },
  {
    day: 3,
    date: 'Troisième Jour',
    activities: [
      {
        time: '9:00 AM',
        title: 'Marché aux Poissons de Tsukiji',
        location: 'Tsukiji, Chuo City, Tokyo 104-0045, Japan',
        description:
          "Découvrez l'atmosphère unique du célèbre marché aux poissons et dégustez des sushis frais dans les restaurants environnants.",
        duration: '2.5 heures',
        type: 'culture',
        icon: Utensils,
        rating: '4.5/5',
        reviews: '(1876 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3541-2640',
      },
      {
        time: '12:00 PM',
        title: 'Quartier de Harajuku',
        location: 'Harajuku, Shibuya City, Tokyo 150-0001, Japan',
        description:
          'Explorez le quartier coloré de Harajuku, célèbre pour sa mode avant-gardiste, sa culture pop et ses boutiques excentriques.',
        duration: '2 heures',
        type: 'exploration',
        icon: Camera,
        rating: '4.3/5',
        reviews: '(2341 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3379-5511',
      },
      {
        time: '2:30 PM',
        title: 'Sanctuaire Meiji',
        location: '1-1 Kamizono-cho, Shibuya City, Tokyo 151-8557, Japan',
        description:
          "Visitez ce sanctuaire shintoïste dédié à l'empereur Meiji, entouré d'une forêt urbaine paisible au cœur de Tokyo.",
        duration: '1.5 heures',
        type: 'culture',
        icon: TreePine,
        rating: '4.4/5',
        reviews: '(1987 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3379-5511',
      },
      {
        time: '7:00 PM',
        title: 'Dîner à Shinjuku - Memory Lane',
        location: 'Shinjuku City, Tokyo 160-0023, Japan',
        description:
          'Découvrez les petits bars et restaurants traditionnels de Memory Lane (Omoide Yokocho) pour une expérience authentique.',
        duration: '2 heures',
        type: 'dining',
        icon: Utensils,
        rating: '4.2/5',
        reviews: '(1543 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3344-1111',
      },
    ],
  },
  {
    day: 4,
    date: 'Quatrième Jour',
    activities: [
      {
        time: '9:30 AM',
        title: "Quartier d'Ueno & Parc d'Ueno",
        location: 'Ueno, Taito City, Tokyo 110-0007, Japan',
        description:
          "Découvrez les musées, le zoo et le magnifique parc d'Ueno, centre culturel de Tokyo avec ses cerisiers et ses temples.",
        duration: '3 heures',
        type: 'culture',
        icon: TreePine,
        rating: '4.2/5',
        reviews: '(1654 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3828-5171',
      },
      {
        time: '1:00 PM',
        title: 'Déjeuner à Yanaka',
        location: 'Yanaka, Taito City, Tokyo 110-0001, Japan',
        description:
          'Explorez le quartier traditionnel de Yanaka et déjeunez dans un restaurant local authentique du vieux Tokyo.',
        duration: '1.5 heures',
        type: 'dining',
        icon: Utensils,
        rating: '4.1/5',
        reviews: '(876 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3821-4456',
      },
      {
        time: '3:00 PM',
        title: 'Temple Kiyomizu Kannon-do',
        location: '1-29 Ueno Park, Taito City, Tokyo 110-0007, Japan',
        description:
          "Visitez ce temple bouddhiste historique situé dans le parc d'Ueno, offrant une architecture traditionnelle magnifique.",
        duration: '1 heure',
        type: 'culture',
        icon: TreePine,
        rating: '4.3/5',
        reviews: '(1234 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3821-4749',
      },
      {
        time: '7:30 PM',
        title: 'Dîner à Akasaka',
        location: 'Akasaka, Minato City, Tokyo 107-0052, Japan',
        description:
          "Savourez un dîner raffiné dans le quartier d'Akasaka, connu pour ses restaurants haut de gamme.",
        duration: '2 heures',
        type: 'fine-dining',
        icon: Utensils,
        rating: '4.6/5',
        reviews: '(1987 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3582-2111',
      },
    ],
  },
  {
    day: 5,
    date: 'Cinquième Jour',
    activities: [
      {
        time: '8:30 AM',
        title: 'Marché aux Poissons de Toyosu',
        location: '6 Chome-6-2 Toyosu, Koto City, Tokyo 135-0061, Japan',
        description:
          "Visitez le nouveau marché aux poissons de Toyosu et assistez aux enchères de thon depuis les galeries d'observation.",
        duration: '2 heures',
        type: 'experience',
        icon: Eye,
        rating: '4.4/5',
        reviews: '(2156 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3520-8205',
      },
      {
        time: '11:00 AM',
        title: 'Odaiba & TeamLab Borderless',
        location: '1 Chome-3-15 Odaiba, Minato City, Tokyo 135-0091, Japan',
        description:
          "Explorez l'île artificielle d'Odaiba et découvrez l'art numérique immersif au musée TeamLab Borderless.",
        duration: '3 heures',
        type: 'experience',
        icon: Star,
        rating: '4.7/5',
        reviews: '(3421 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-6368-4292',
      },
      {
        time: '3:00 PM',
        title: "Plage d'Odaiba & Aqua City",
        location: '1 Chome-7-1 Daiba, Minato City, Tokyo 135-0091, Japan',
        description:
          "Détendez-vous sur la plage artificielle d'Odaiba et explorez le centre commercial Aqua City avec vue sur la baie.",
        duration: '2 heures',
        type: 'shopping',
        icon: Waves,
        rating: '4.1/5',
        reviews: '(1654 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3599-4700',
      },
      {
        time: '7:00 PM',
        title: 'Dîner avec vue sur Tokyo Bay',
        location: 'Odaiba, Minato City, Tokyo 135-0091, Japan',
        description:
          'Dînez dans un restaurant avec vue panoramique sur la baie de Tokyo et les illuminations nocturnes.',
        duration: '2 heures',
        type: 'fine-dining',
        icon: Utensils,
        rating: '4.5/5',
        reviews: '(1876 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3529-1111',
      },
    ],
  },
  {
    day: 6,
    date: 'Sixième Jour',
    activities: [
      {
        time: '9:00 AM',
        title: 'Quartier de Kichijoji & Parc Inokashira',
        location: 'Kichijoji, Musashino, Tokyo 180-0004, Japan',
        description:
          'Explorez le charmant quartier de Kichijoji et promenez-vous dans le magnifique parc Inokashira avec son lac paisible.',
        duration: '2.5 heures',
        type: 'nature',
        icon: TreePine,
        rating: '4.3/5',
        reviews: '(1543 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 422-47-6900',
      },
      {
        time: '12:00 PM',
        title: 'Musée Ghibli',
        location: '1 Chome-1-83 Shimorenjaku, Mitaka, Tokyo 181-0013, Japan',
        description:
          "Découvrez l'univers magique du Studio Ghibli dans ce musée unique dédié aux films d'animation de Miyazaki.",
        duration: '2 heures',
        type: 'culture',
        icon: Star,
        rating: '4.8/5',
        reviews: '(4521 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 570-055-777',
      },
      {
        time: '3:00 PM',
        title: 'Quartier de Shimokitazawa',
        location: 'Shimokitazawa, Setagaya City, Tokyo 155-0031, Japan',
        description:
          'Explorez ce quartier bohème connu pour ses boutiques vintage, ses théâtres indépendants et ses cafés branchés.',
        duration: '2 heures',
        type: 'exploration',
        icon: Camera,
        rating: '4.2/5',
        reviews: '(1234 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3468-2211',
      },
      {
        time: '7:30 PM',
        title: 'Dîner à Ebisu',
        location: 'Ebisu, Shibuya City, Tokyo 150-0013, Japan',
        description:
          "Savourez un dîner dans le quartier d'Ebisu, réputé pour ses restaurants gastronomiques et ses bars.",
        duration: '2 heures',
        type: 'fine-dining',
        icon: Utensils,
        rating: '4.4/5',
        reviews: '(1765 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-5424-1111',
      },
    ],
  },
  {
    day: 7,
    date: 'Septième Jour',
    activities: [
      {
        time: '8:00 AM',
        title: 'Excursion Mont Fuji - Kawaguchi-ko',
        location:
          'Kawaguchi-ko, Minamitsuru District, Yamanashi 401-0301, Japan',
        description:
          "Excursion d'une journée vers le mont Fuji emblématique avec vues spectaculaires depuis le lac Kawaguchi et expérience culturelle.",
        duration: 'Journée complète',
        type: 'nature',
        icon: Mountain,
        rating: '4.8/5',
        reviews: '(3421 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 555-72-0259',
      },
    ],
  },
  {
    day: 8,
    date: 'Huitième Jour',
    activities: [
      {
        time: '10:00 AM',
        title: 'Quartier de Nihonbashi',
        location: 'Nihonbashi, Chuo City, Tokyo 103-0027, Japan',
        description:
          "Explorez le quartier historique de Nihonbashi, ancien centre commercial d'Edo, avec ses boutiques traditionnelles et modernes.",
        duration: '2 heures',
        type: 'exploration',
        icon: Building,
        rating: '4.1/5',
        reviews: '(987 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3271-4800',
      },
      {
        time: '12:30 PM',
        title: 'Déjeuner à Tokyo Station',
        location: '1 Chome-9-1 Marunouchi, Chiyoda City, Tokyo 100-0005, Japan',
        description:
          "Savourez un déjeuner dans l'un des nombreux restaurants de la gare de Tokyo, hub gastronomique de la ville.",
        duration: '1 heure',
        type: 'dining',
        icon: Utensils,
        rating: '4.2/5',
        reviews: '(1543 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3212-2111',
      },
      {
        time: '2:00 PM',
        title: 'Quartier de Marunouchi & Shopping',
        location: 'Marunouchi, Chiyoda City, Tokyo 100-0005, Japan',
        description:
          "Explorez le quartier d'affaires de Marunouchi avec ses centres commerciaux haut de gamme et son architecture moderne.",
        duration: '2 heures',
        type: 'shopping',
        icon: Building,
        rating: '4.3/5',
        reviews: '(1234 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-5218-5100',
      },
      {
        time: '5:00 PM',
        title: 'Dernières Emplettes à Ginza',
        location: 'Ginza, Chuo City, Tokyo 104-0061, Japan',
        description:
          'Dernière session shopping dans le prestigieux quartier de Ginza pour vos achats de souvenirs et produits de luxe.',
        duration: '2 heures',
        type: 'shopping',
        icon: Star,
        rating: '4.5/5',
        reviews: '(2876 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3572-5755',
      },
      {
        time: '8:00 PM',
        title: "Dîner d'Adieu - Restaurant Kaiseki",
        location: 'Ginza, Chuo City, Tokyo 104-0061, Japan',
        description:
          "Terminez votre séjour par un dîner kaiseki traditionnel, l'art culinaire japonais le plus raffiné, dans un restaurant étoilé.",
        duration: '2.5 heures',
        type: 'fine-dining',
        icon: Utensils,
        rating: '4.9/5',
        reviews: '(1987 avis)',
        image: '/placeholder.svg?height=160&width=240',
        phone: '+81 3-3571-1234',
      },
    ],
  },
];

const getTypeColor = (type: string) => {
  const colors = {
    culture: 'bg-amber-900/30 text-amber-300 border-amber-700/50',
    dining: 'bg-rose-900/30 text-rose-300 border-rose-700/50',
    'fine-dining': 'bg-purple-900/30 text-purple-300 border-purple-700/50',
    sightseeing: 'bg-blue-900/30 text-blue-300 border-blue-700/50',
    exploration: 'bg-green-900/30 text-green-300 border-green-700/50',
    experience: 'bg-indigo-900/30 text-indigo-300 border-indigo-700/50',
    nature: 'bg-emerald-900/30 text-emerald-300 border-emerald-700/50',
    shopping: 'bg-pink-900/30 text-pink-300 border-pink-700/50',
  };
  return (
    colors[type as keyof typeof colors] ||
    'bg-gray-900/30 text-gray-300 border-gray-700/50'
  );
};

const getTypeLabel = (type: string) => {
  const labels = {
    culture: 'Culture',
    dining: 'Gastronomie',
    'fine-dining': 'Haute Gastronomie',
    sightseeing: 'Visite',
    exploration: 'Exploration',
    experience: 'Expérience',
    nature: 'Nature',
    shopping: 'Shopping',
  };
  return labels[type as keyof typeof labels] || type;
};

// Fonction pour grouper les jours par semaines
const groupDaysByWeeks = (days: typeof itineraryData) => {
  if (days.length <= 7) {
    return [{ type: 'days', days }];
  }

  const weeks = [];
  let weekNumber = 1;

  for (let i = 0; i < days.length; i += 7) {
    const weekDays = days.slice(i, i + 7);
    if (weekDays.length === 7) {
      weeks.push({
        type: 'week',
        weekNumber,
        title:
          weekNumber === 1
            ? 'Première semaine'
            : weekNumber === 2
              ? 'Deuxième semaine'
              : weekNumber === 3
                ? 'Troisième semaine'
                : weekNumber === 4
                  ? 'Quatrième semaine'
                  : `${weekNumber}ème semaine`,
        days: weekDays,
      });
      weekNumber++;
    } else {
      // Jours restants
      weeks.push({
        type: 'remaining',
        title:
          weekDays.length === 1
            ? 'Jour supplémentaire'
            : 'Jours supplémentaires',
        days: weekDays,
      });
    }
  }

  return weeks;
};

export default function Component() {
  const [expandedWeeks, setExpandedWeeks] = useState<Set<string>>(
    new Set(['week-1']),
  ); // Première semaine ouverte par défaut
  const [selectedDay, setSelectedDay] = useState<number | null>(null); // Pour le modal

  const groupedData = groupDaysByWeeks(itineraryData);

  const toggleWeek = (weekId: string) => {
    const newExpandedWeeks = new Set(expandedWeeks);
    if (newExpandedWeeks.has(weekId)) {
      newExpandedWeeks.delete(weekId);
    } else {
      newExpandedWeeks.add(weekId);
    }
    setExpandedWeeks(newExpandedWeeks);
  };

  const openDayModal = (dayNumber: number) => {
    setSelectedDay(dayNumber);
  };

  const closeDayModal = () => {
    setSelectedDay(null);
  };

  const expandAllWeeks = () => {
    const allWeekIds = groupedData.map((group, index) =>
      group.type === 'week'
        ? `week-${group.weekNumber}`
        : group.type === 'remaining'
          ? 'remaining'
          : 'days',
    );
    setExpandedWeeks(new Set(allWeekIds));
  };

  const collapseAllWeeks = () => {
    setExpandedWeeks(new Set());
  };

  const selectedDayData = selectedDay
    ? itineraryData.find((day) => day.day === selectedDay)
    : null;

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-6 py-10 max-w-5xl">
        {/* Header - Centré */}
        <div className="mb-10 text-center">
          <div className="flex items-center justify-center gap-3 mb-2">
            <div className="w-8 h-8 bg-blue-800/80 rounded-full flex items-center justify-center">
              <Star className="w-4 h-4 text-white" />
            </div>
            <h1 className="text-xl font-light text-white tracking-wide">
              Day-by-Day Itinerary
            </h1>
          </div>
          <p className="text-blue-400/80 text-sm font-light mb-6">
            Discover exceptional experiences • Comfort & Luxury Combined
          </p>

          {/* Expand/Collapse Controls - Centrés */}
          <div className="flex flex-wrap justify-center gap-2">
            {groupedData.length > 1 &&
              groupedData.some((g) => g.type === 'week') && (
                <>
                  <Button
                    onClick={expandAllWeeks}
                    variant="outline"
                    size="sm"
                    className="bg-purple-800/50 border-purple-700/50 text-purple-300 hover:bg-purple-700/50 rounded-full text-xs font-light"
                  >
                    <Calendar className="w-3 h-3 mr-1" />
                    Toutes les semaines
                  </Button>
                  <Button
                    onClick={collapseAllWeeks}
                    variant="outline"
                    size="sm"
                    className="bg-purple-800/50 border-purple-700/50 text-purple-300 hover:bg-purple-700/50 rounded-full text-xs font-light"
                  >
                    <Minus className="w-3 h-3 mr-1" />
                    Réduire semaines
                  </Button>
                </>
              )}
          </div>
        </div>

        {/* Itinerary */}
        <div className="space-y-6">
          {groupedData.map((group, groupIndex) => {
            if (group.type === 'days') {
              // Affichage direct des jours (moins de 7 jours) - Horizontal
              return (
                <div key="days" className="space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    {group.days.map((day) => (
                      <Card
                        key={day.day}
                        className="group cursor-pointer hover:shadow-xl transition-all duration-300 border border-gray-800/50 shadow-md bg-black/50 backdrop-blur-sm rounded-2xl overflow-hidden"
                        onClick={() => openDayModal(day.day)}
                      >
                        <div className="relative h-32">
                          <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent z-10"></div>
                          <img
                            src={day.activities[0]?.image || '/placeholder.svg'}
                            alt={day.date}
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute top-2 left-2 z-20">
                            <div className="w-8 h-8 bg-blue-800/80 rounded-full flex items-center justify-center">
                              <span className="text-xs font-medium text-white">
                                {day.day}
                              </span>
                            </div>
                          </div>
                          <div className="absolute bottom-2 left-2 z-20">
                            <h3 className="text-sm font-light text-white">
                              {day.date}
                            </h3>
                            <p className="text-xs text-gray-300 font-light">
                              {day.activities.length} activités
                            </p>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              );
            } else {
              // Affichage avec groupement par semaines
              const weekId =
                group.type === 'week'
                  ? `week-${group.weekNumber}`
                  : 'remaining';
              const isWeekExpanded = expandedWeeks.has(weekId);

              return (
                <div key={weekId} className="relative">
                  {/* Week Header - Clickable */}
                  <div
                    className="flex items-center justify-between gap-4 mb-6 p-5 bg-purple-900/20 rounded-2xl border border-purple-800/30 cursor-pointer hover:bg-purple-900/30 transition-all duration-300"
                    onClick={() => toggleWeek(weekId)}
                  >
                    <div className="flex items-center gap-4">
                      <div className="flex-shrink-0">
                        <div className="w-14 h-14 bg-purple-800/80 rounded-2xl flex items-center justify-center shadow-lg">
                          <Calendar className="w-6 h-6 text-white" />
                        </div>
                      </div>
                      <div>
                        <h2 className="text-xl font-light text-white mb-1">
                          {group.title}
                        </h2>
                        <p className="text-purple-300/80 text-sm font-light">
                          {group.days.length} jour
                          {group.days.length > 1 ? 's' : ''} • Jours{' '}
                          {group.days[0].day} à{' '}
                          {group.days[group.days.length - 1].day}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <div className="text-sm text-purple-400 font-light">
                        {isWeekExpanded ? 'Réduire' : 'Développer'}
                      </div>
                      {isWeekExpanded ? (
                        <ChevronUp className="w-6 h-6 text-purple-400" />
                      ) : (
                        <ChevronDown className="w-6 h-6 text-purple-400" />
                      )}
                    </div>
                  </div>

                  {/* Week Content - Collapsible - Horizontal Days */}
                  <div
                    className={`overflow-hidden transition-all duration-500 ease-in-out ${
                      isWeekExpanded
                        ? 'max-h-[500px] opacity-100'
                        : 'max-h-0 opacity-0'
                    }`}
                  >
                    <div className="ml-8 pb-6">
                      {/* Conteneur avec défilement horizontal */}
                      <div className="overflow-x-auto pb-2">
                        <div className="flex gap-4 min-w-max">
                          {group.days.map((day) => (
                            <Card
                              key={day.day}
                              className="group cursor-pointer hover:shadow-xl transition-all duration-300 border border-gray-800/50 shadow-md bg-black/50 backdrop-blur-sm rounded-2xl overflow-hidden flex-shrink-0 w-64"
                              onClick={() => openDayModal(day.day)}
                            >
                              <div className="relative h-32">
                                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent z-10" />
                                <img
                                  src={
                                    day.activities[0]?.image ||
                                    '/placeholder.svg'
                                  }
                                  alt={day.date}
                                  className="w-full h-full object-cover"
                                />
                                <div className="absolute top-2 left-2 z-20">
                                  <div className="w-8 h-8 bg-blue-800/80 rounded-full flex items-center justify-center">
                                    <span className="text-xs font-medium text-white">
                                      {day.day}
                                    </span>
                                  </div>
                                </div>
                                <div className="absolute bottom-2 left-2 z-20">
                                  <h3 className="text-sm font-light text-white">
                                    {day.date}
                                  </h3>
                                  <p className="text-xs text-gray-300 font-light">
                                    {day.activities.length} activités
                                  </p>
                                </div>
                              </div>
                            </Card>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            }
          })}
        </div>

        {/* Modal pour afficher les détails d'un jour */}
        {selectedDay && selectedDayData && (
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-black border border-gray-800/50 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              {/* Modal Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-800/50">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-blue-800/80 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-white">
                      {selectedDayData.day}
                    </span>
                  </div>
                  <div>
                    <h2 className="text-xl font-light text-white">
                      {selectedDayData.date}
                    </h2>
                    <p className="text-gray-400 text-sm font-light">
                      {selectedDayData.activities.length} expériences
                      soigneusement sélectionnées
                    </p>
                  </div>
                </div>
                <Button
                  onClick={closeDayModal}
                  variant="ghost"
                  size="sm"
                  className="text-gray-400 hover:text-white"
                >
                  <X className="w-5 h-5" />
                </Button>
              </div>

              {/* Modal Content */}
              <div className="p-6">
                <div className="space-y-6">
                  {selectedDayData.activities.map((activity, index) => {
                    const IconComponent = activity.icon;
                    return (
                      <div key={index} className="relative">
                        <Card className="group hover:shadow-xl transition-all duration-300 border border-gray-800/50 shadow-md bg-black/50 backdrop-blur-sm rounded-2xl overflow-hidden">
                          <div className="flex flex-col sm:flex-row">
                            {/* Image Section */}
                            <div className="relative sm:w-60 h-40 sm:h-auto flex-shrink-0">
                              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent z-10 rounded-2xl"></div>
                              <img
                                src={activity.image || '/placeholder.svg'}
                                alt={activity.title}
                                className="w-full h-full object-cover rounded-t-2xl sm:rounded-l-2xl sm:rounded-t-none"
                              />
                              <div className="absolute bottom-2 left-2 z-20">
                                <div className="bg-black/60 backdrop-blur-sm px-2 py-1 rounded-full flex items-center">
                                  <Star className="w-3 h-3 text-yellow-400 mr-1" />
                                  <span className="text-white text-xs font-light">
                                    {activity.rating}
                                  </span>
                                  <span className="text-gray-300 text-xs ml-1 font-light">
                                    {activity.reviews}
                                  </span>
                                </div>
                              </div>
                            </div>

                            {/* Content Section */}
                            <CardContent className="flex-1 p-4">
                              <div className="flex items-start justify-between gap-3 mb-3">
                                <div className="flex items-center gap-2">
                                  <div className="w-8 h-8 bg-gray-800/50 rounded-full flex items-center justify-center group-hover:bg-blue-900/50 transition-all duration-300">
                                    <IconComponent className="w-4 h-4 text-gray-400 group-hover:text-blue-400 transition-colors duration-300" />
                                  </div>
                                  <div>
                                    <Badge
                                      variant="outline"
                                      className={`${getTypeColor(activity.type)} text-xs font-light rounded-full px-2 py-0.5`}
                                    >
                                      {getTypeLabel(activity.type)}
                                    </Badge>
                                  </div>
                                </div>
                                <div className="text-right flex-shrink-0">
                                  <div className="text-sm font-light text-blue-400">
                                    {activity.time}
                                  </div>
                                  <div className="flex items-center gap-1 text-gray-500 mt-0.5">
                                    <Clock className="w-3 h-3" />
                                    <span className="text-xs font-light">
                                      {activity.duration}
                                    </span>
                                  </div>
                                </div>
                              </div>

                              <h4 className="text-base font-light text-white mb-2 group-hover:text-blue-400 transition-colors duration-300 leading-snug">
                                {activity.title}
                              </h4>

                              <p className="text-gray-400 text-sm font-light leading-relaxed mb-3">
                                {activity.description}
                              </p>

                              <div className="space-y-1 text-xs text-gray-500 mb-3">
                                <div className="flex items-start gap-1.5">
                                  <MapPin className="w-3 h-3 flex-shrink-0 mt-0.5" />
                                  <p className="leading-relaxed font-light">
                                    {activity.location}
                                  </p>
                                </div>
                                <div className="flex items-start gap-1.5">
                                  <Phone className="w-3 h-3 flex-shrink-0 mt-0.5" />
                                  <p className="leading-relaxed font-light">
                                    {activity.phone}
                                  </p>
                                </div>
                              </div>

                              <Button className="w-full bg-blue-800/80 hover:bg-blue-700/80 text-white text-xs font-light py-2 rounded-full transition-all duration-300 backdrop-blur-sm">
                                View Details & Map
                              </Button>
                            </CardContent>
                          </div>
                        </Card>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="text-center mt-16 pt-8 border-t border-gray-800/50">
          <div className="inline-flex items-center gap-2 text-gray-400 mb-2">
            <Star className="w-4 h-4 text-blue-400/80" />
            <span className="font-light text-sm">Voyage d'Exception</span>
            <Star className="w-4 h-4 text-blue-400/80" />
          </div>
          <p className="text-gray-500 font-light text-xs">
            Créé avec passion pour une expérience inoubliable à Tokyo
          </p>
        </div>
      </div>
    </div>
  );
}
