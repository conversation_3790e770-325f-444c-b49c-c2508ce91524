import {
  type UIMessage,
  appendClientMessage,
  appendResponseMessages,
  createDataStreamResponse,
  smoothStream,
  streamText,
} from 'ai';
import { auth, type UserType } from '@/app/(auth)/auth';
import { systemPrompt, type RequestHints } from '@/lib/ai/prompts';
import {
  deleteChatById,
  getChatById,
  getMessageCountByUserId,
  getMessagesByChatId,
  saveChat,
  saveMessages,
} from '@/lib/db/queries';
import { generateUUID, getTrailingMessageId } from '@/lib/utils';
import { generateTitleFromUserMessage } from '../../actions';
import { createDocument } from '@/lib/ai/tools/create-document';
import { updateDocument } from '@/lib/ai/tools/update-document';
import { requestSuggestions } from '@/lib/ai/tools/request-suggestions';
import { getWeather } from '@/lib/ai/tools/get-weather';
import { memoryRetrieval } from '@/lib/ai/tools/memory-retrieval';
import { remember } from '@/lib/ai/tools/remember';
import { isProductionEnvironment } from '@/lib/constants';
import { myProvider } from '@/lib/ai/providers';
import { entitlementsByUserType } from '@/lib/ai/entitlements';
import { z } from 'zod';
import { tool } from 'ai';
import { MemoryClient } from 'mem0ai';
import { enqueueMemory } from '@/lib/memoryBatcher';
import { createResumableStream } from '@/lib/resumable-stream';
import { config } from 'dotenv';
import { web_search } from '@/lib/ai/tools/web-search';
import { youtube_search } from '@/lib/ai/tools/youtube-search';
import { map_search } from '@/lib/ai/tools/map-search';
import FirecrawlApp from '@mendable/firecrawl-js';
import { geolocation } from '@vercel/functions';

// Load environment variables
config({
  path: '.env',
});

export const maxDuration = 60;

// Fonction améliorée pour sauvegarder les messages dans la mémoire
async function saveMessageToMemory(
  _client: MemoryClient, // Préfixé avec _ pour indiquer qu'il n'est pas utilisé
  message: UIMessage | any,
  userId: string,
  _orgId: string, // Préfixé avec _ pour indiquer qu'il n'est pas utilisé
  _projectId: string, // Préfixé avec _ pour indiquer qu'il n'est pas utilisé
  chatId: string,
  _timeoutMs = 10000, // Préfixé avec _ pour indiquer qu'il n'est pas utilisé
) {
  try {
    // Extraire le contenu du message
    const messageContent = message.parts
      ? message.parts
          .map((part: any) =>
            typeof part === 'string' ? part : JSON.stringify(part),
          )
          .join(' ')
      : message.content || '';

    // Formater le contenu avec le rôle pour une meilleure lisibilité
    const content = `Message (${message.role}): ${messageContent}`;

    // Ajouter des métadonnées enrichies
    const metadata: Record<string, any> = {
      messageId: message.id,
      chatId,
      timestamp: new Date().toISOString(),
      role: message.role,
      memory_type: 'conversation',
      // Stocker les catégories dans les métadonnées
      categories_info: ['message', message.role].join(','),
    };

    // Analyser le contenu pour détecter les informations personnelles potentielles
    // Cette analyse simple peut être améliorée avec des techniques plus avancées
    const personalInfoPatterns = [
      {
        pattern: /mon nom est|je m'appelle|je suis/i,
        type: 'demographic',
        category: 'name',
      },
      {
        pattern: /mon email|mon courriel|mon adresse e-mail/i,
        type: 'contact',
        category: 'email',
      },
      {
        pattern: /mon téléphone|mon numéro|mon portable/i,
        type: 'contact',
        category: 'phone',
      },
      {
        pattern: /j'aime|je préfère|ma préférence/i,
        type: 'preference',
        category: 'general',
      },
      {
        pattern: /ma langue|je parle|langue maternelle/i,
        type: 'demographic',
        category: 'language',
      },
      {
        pattern: /mon pays|j'habite|je vis à/i,
        type: 'demographic',
        category: 'location',
      },
      {
        pattern: /mon hobby|mon loisir|ma passion/i,
        type: 'preference',
        category: 'hobby',
      },
    ];

    // Si c'est un message utilisateur, vérifier s'il contient des informations personnelles
    if (message.role === 'user') {
      for (const { pattern, type, category } of personalInfoPatterns) {
        if (pattern.test(messageContent)) {
          // Ajouter des métadonnées pour les informations personnelles
          metadata.may_contain_personal_info = true;
          metadata.potential_info_type = type;
          metadata.potential_info_category = category;

          // Ajouter des catégories dans les métadonnées
          metadata.categories_info += `,potential_personal_info,${type},${category}`;

          break; // Arrêter après la première correspondance pour simplifier
        }
      }

      // Utiliser le batcher avec priorité pour les messages utilisateur
      enqueueMemory({
        data: content,
        userId,
        metadata,
        priority: true, // Priorité pour les messages utilisateur
      });
    } else {
      // Messages assistant en mode normal
      enqueueMemory({
        data: content,
        userId,
        metadata,
      });
    }

    return true;
  } catch (error) {
    // Log sécurisé sans exposer le contenu du message
    logError('Failed to save message to memory:', {
      messageId: message.id,
      role: message.role,
      error: error instanceof Error ? error.message : String(error),
    });
    return false;
  }
}

// 1. Centralisation de la configuration Mem0 avec cache
// Cache pour stocker les configurations par userId
const memoryEnvCache = new Map<
  string,
  {
    apiKey: string;
    orgId: string;
    projectId: string;
    userId: string;
  }
>();

function getMemoryEnv(userId?: string) {
  if (!userId) {
    throw new Error('User ID is required for memory operations');
  }

  // Vérifier si la configuration est déjà en cache
  const cachedConfig = memoryEnvCache.get(userId);
  if (cachedConfig) {
    return cachedConfig;
  }

  const { MEM0_API_KEY, MEM0_ORG_ID, MEM0_PROJECT_ID } = process.env;

  if (!MEM0_API_KEY || !MEM0_ORG_ID || !MEM0_PROJECT_ID) {
    throw new Error('Missing Mem0 environment variables');
  }

  // Créer la configuration
  const config = {
    apiKey: MEM0_API_KEY,
    orgId: MEM0_ORG_ID,
    projectId: MEM0_PROJECT_ID,
    userId,
  };

  // Mettre en cache la configuration
  memoryEnvCache.set(userId, config);

  return config;
}

// 2. Wrapper de logging conditionnel
const log = (...args: any[]) => {
  if (!isProductionEnvironment) console.log(...args);
};

const logError = (message: string, error: unknown) => {
  // Extraire les informations utiles de l'erreur sans exposer de données sensibles
  const errorInfo =
    error instanceof Error
      ? {
          name: error.name,
          message: error.message,
          stack: isProductionEnvironment
            ? undefined
            : error.stack?.split('\n').slice(0, 3).join('\n'),
        }
      : typeof error === 'object' && error !== null
        ? { ...error } // Si c'est déjà un objet, l'utiliser tel quel
        : { message: String(error) };

  console.error(`${message}`, errorInfo);
};

export async function POST(request: Request) {
  try {
    // Récupérer et valider les données de la requête
    const requestData = await request.json();

    // Validation de base des champs requis
    if (!requestData || typeof requestData !== 'object') {
      return new Response('Invalid request format', { status: 400 });
    }

    const {
      id,
      messages: originalMessages,
      selectedChatModel,
      timezone,
    } = requestData;

    // Validation des champs individuels
    if (!id || typeof id !== 'string') {
      return new Response('Invalid or missing chat ID', { status: 400 });
    }

    if (!Array.isArray(originalMessages) || originalMessages.length === 0) {
      return new Response('Invalid or empty messages array', { status: 400 });
    }

    if (!selectedChatModel || typeof selectedChatModel !== 'string') {
      return new Response('Invalid or missing chat model', { status: 400 });
    }

    // Timezone est optionnel, utiliser une valeur par défaut si non fourni
    const validTimezone =
      timezone && typeof timezone === 'string' ? timezone : 'UTC';

    // Log sécurisé de la timezone
    log('Request info:', {
      hasTimezone: !!validTimezone,
      messageCount: originalMessages.length,
      selectedModel: selectedChatModel,
    });
    // Create a mutable copy of messages
    const messages = [...originalMessages];

    // Récupérer le message le plus récent de l'utilisateur
    const message = messages[messages.length - 1];

    const session = await auth();

    if (!session?.user) {
      return new Response('Unauthorized', { status: 401 });
    }

    const userType: UserType = session.user.type;

    const messageCount = await getMessageCountByUserId({
      id: session.user.id,
      differenceInHours: 24,
    });

    if (messageCount > entitlementsByUserType[userType].maxMessagesPerDay) {
      return new Response(
        'You have exceeded your maximum number of messages for the day! Please try again later.',
        {
          status: 429,
        },
      );
    }

    const chat = await getChatById({ id });

    if (!chat) {
      const title = await generateTitleFromUserMessage({
        message: message as UIMessage,
      });

      await saveChat({ id, userId: session.user.id, title });
    } else {
      if (chat.userId !== session.user.id) {
        return new Response('Forbidden', { status: 403 });
      }
    }

    // Récupérer les messages précédents
    const previousMessages = await getMessagesByChatId({ id });

    // Ajouter le nouveau message utilisateur
    const updatedMessages = appendClientMessage({
      // @ts-expect-error: todo add type conversion from DBMessage[] to UIMessage[]
      messages: previousMessages,
      message,
    });

    // Obtenir les informations de géolocalisation seulement si nécessaire
    // Initialiser avec des valeurs par défaut
    let requestHints: RequestHints = {
      longitude: undefined,
      latitude: undefined,
      city: undefined,
      country: undefined,
    };

    // Vérifier si le modèle utilise les informations de géolocalisation
    const needsGeolocation = true; // Par défaut, on considère que tous les modèles en ont besoin

    if (needsGeolocation) {
      try {
        const geoData = geolocation(request);
        requestHints = {
          longitude: geoData.longitude,
          latitude: geoData.latitude,
          city: geoData.city,
          country: geoData.country,
        };

        // Log sécurisé (sans coordonnées précises)
        log('Geolocation data retrieved:', {
          hasCity: !!geoData.city,
          hasCountry: !!geoData.country,
          hasCoordinates: !!(geoData.longitude && geoData.latitude),
        });
      } catch (error) {
        logError('Failed to retrieve geolocation data:', error);
        // Continuer avec les valeurs par défaut
      }
    }

    if (message.content === 'ping') {
      const resumableStream = await createResumableStream({ chatId: id });

      if (resumableStream) {
        return new Response(resumableStream, { status: 200 });
      } else {
        return new Response(null, { status: 204 });
      }
    }

    await saveMessages({
      messages: [
        {
          chatId: id,
          id: message.id,
          role: 'user',
          parts: message.parts,
          attachments: message.experimental_attachments ?? [],
          createdAt: new Date(),
        },
      ],
    });

    // Initialiser le client mémoire une seule fois
    let memoryClient: MemoryClient | null = null;
    let memoryConfig: {
      apiKey: string;
      orgId: string;
      projectId: string;
      userId: string;
    } | null = null;

    const messagesWithoutPings = updatedMessages.filter(
      (msg) => msg.content !== 'ping',
    );

    try {
      memoryConfig = getMemoryEnv(session.user.id);
      memoryClient = new MemoryClient({ apiKey: memoryConfig.apiKey });
    } catch (error) {
      logError('Failed to initialize memory client:', error);
    }

    // Sauvegarder le message utilisateur dans Mem0 avant de commencer le streaming
    let userMessageSavedToMem0 = false;
    if (memoryClient && memoryConfig) {
      try {
        userMessageSavedToMem0 = await saveMessageToMemory(
          memoryClient,
          message,
          memoryConfig.userId,
          memoryConfig.orgId,
          memoryConfig.projectId,
          id,
          15000,
        );

        log(
          'User message saved to memory before streaming:',
          userMessageSavedToMem0,
        );
      } catch (error) {
        logError(
          'Failed to save user message to memory before streaming:',
          error,
        );
      }
    }

    return createDataStreamResponse({
      execute: (dataStream) => {
        const textStream = streamText({
          model: myProvider.languageModel(selectedChatModel),
          system: systemPrompt({ selectedChatModel, requestHints }),
          messages: messagesWithoutPings,
          maxSteps: 5,
          experimental_activeTools:
            selectedChatModel === 'chat-model-reasoning'
              ? [
                  'memory_manager',
                  'memoryRetrieval',
                  'remember',
                  'datetime',
                  'web_search',
                  'youtube_search',
                  'map_search',
                  'retrieve',
                ]
              : [
                  'getWeather',
                  'createDocument',
                  'updateDocument',
                  'requestSuggestions',
                  'memoryRetrieval',
                  'memory_manager',
                  'remember',
                  'datetime',
                  'web_search',
                  'youtube_search',
                  'map_search',
                  'retrieve',
                ],
          experimental_transform: smoothStream({ chunking: 'word' }),
          experimental_generateMessageId: generateUUID,
          tools: {
            getWeather,
            createDocument: createDocument({ session, dataStream }),
            updateDocument: updateDocument({ session, dataStream }),
            web_search: web_search({ session, dataStream }),
            youtube_search: youtube_search({ session, dataStream }),
            map_search: map_search({ session, dataStream }),
            requestSuggestions: requestSuggestions({
              session,
              dataStream,
            }),
            memoryRetrieval,
            remember,
            retrieve: tool({
              description:
                'Retrieve the information from a URL using Firecrawl.',
              parameters: z.object({
                url: z
                  .string()
                  .describe('The URL to retrieve the information from.'),
              }),
              execute: async ({ url }: { url: string }) => {
                const app = new FirecrawlApp({
                  apiKey: process.env.FIRECRAWL_API_KEY,
                });
                try {
                  const content = await app.scrapeUrl(url, {
                    agent: {
                      model: 'FIRE-1',
                      prompt:
                        'Extract the page title, main content, and a brief description.',
                    },
                  });
                  if (!content.success || !content.metadata) {
                    return {
                      results: [
                        {
                          error: content.error,
                        },
                      ],
                    };
                  }

                  // Define schema for extracting missing content
                  const schema = z.object({
                    title: z.string(),
                    content: z.string(),
                    description: z.string(),
                  });

                  let title = content.metadata.title;
                  let description = content.metadata.description;
                  let extractedContent = content.markdown;

                  // If any content is missing, use extract to get it
                  if (!title || !description || !extractedContent) {
                    const extractResult = await app.extract([url], {
                      prompt:
                        'Extract the page title, main content, and a brief description.',
                      schema: schema,
                    });

                    if (extractResult.success && extractResult.data) {
                      title = title || extractResult.data.title;
                      description =
                        description || extractResult.data.description;
                      extractedContent =
                        extractedContent || extractResult.data.content;
                    }
                  }

                  return {
                    results: [
                      {
                        title: title || 'Untitled',
                        content: extractedContent || '',
                        url: content.metadata.sourceURL,
                        description: description || '',
                        language: content.metadata.language,
                      },
                    ],
                  };
                } catch (error) {
                  console.error('Firecrawl API error:', error);
                  return { error: 'Failed to retrieve content' };
                }
              },
            }),
            datetime: tool({
              description:
                "Get the current date and time in the user's timezone. Use this tool when the user asks for the current time, date, or both.",
              parameters: z.object({}),
              execute: async () => {
                try {
                  // Déterminer la locale de l'utilisateur de façon dynamique
                  // Utiliser une valeur par défaut puisque nous n'avons pas accès aux en-têtes HTTP ici
                  let userLocale = 'fr-FR';

                  // Fallback sur la locale du navigateur si on est côté client
                  if (typeof window !== 'undefined') {
                    userLocale = navigator.language || userLocale;
                  }

                  // Get current date and time with timezone
                  const now = new Date(
                    new Date().toLocaleString('en-US', {
                      timeZone: validTimezone,
                    }),
                  );

                  // Format date and time using the timezone and user's locale
                  return {
                    timestamp: now.getTime(),
                    iso: now.toISOString(),
                    timezone: validTimezone,
                    locale: userLocale,
                    formatted: {
                      date: new Intl.DateTimeFormat(userLocale, {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        timeZone: validTimezone,
                      }).format(now),
                      time: new Intl.DateTimeFormat(userLocale, {
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: userLocale.startsWith('en'), // Format 24h pour les locales non-anglaises
                        timeZone: validTimezone,
                      }).format(now),
                      dateShort: new Intl.DateTimeFormat(userLocale, {
                        month: 'short',
                        day: 'numeric',
                        year: 'numeric',
                        timeZone: validTimezone,
                      }).format(now),
                      timeShort: new Intl.DateTimeFormat(userLocale, {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: userLocale.startsWith('en'), // Format 24h pour les locales non-anglaises
                        timeZone: validTimezone,
                      }).format(now),
                    },
                  };
                } catch (error) {
                  console.error('Error getting datetime:', error);
                  return {
                    error: 'Failed to get current date and time',
                  };
                }
              },
            }),
            memory_manager: tool({
              description:
                'Manage personal memories with add, search, and personal information operations.',
              parameters: z.object({
                action: z
                  .enum([
                    'add',
                    'search',
                    'add_personal_info',
                    'search_personal_info',
                  ])
                  .describe('The memory operation to perform'),
                content: z
                  .string()
                  .optional()
                  .describe('The memory content for add operation'),
                query: z
                  .string()
                  .optional()
                  .describe('The search query for search operations'),
                info_type: z
                  .string()
                  .optional()
                  .describe(
                    'The type of personal information (e.g., "preference", "contact", "demographic")',
                  ),
                info_category: z
                  .string()
                  .optional()
                  .describe(
                    'The category of personal information (e.g., "name", "email", "language", "hobby")',
                  ),
              }),
              execute: async ({
                action,
                content,
                query,
                info_type,
                info_category,
              }: {
                action:
                  | 'add'
                  | 'search'
                  | 'add_personal_info'
                  | 'search_personal_info';
                content?: string;
                query?: string;
                info_type?: string;
                info_category?: string;
              }) => {
                try {
                  const { apiKey, orgId, projectId, userId } = getMemoryEnv(
                    session.user?.id,
                  );
                  const client = new MemoryClient({ apiKey });

                  // Logs sécurisés sans exposer le contenu complet
                  log('Memory operation:', {
                    action,
                    hasContent: !!content,
                    contentLength: content ? content.length : 0,
                    hasQuery: !!query,
                    queryLength: query ? query.length : 0,
                    info_type,
                    info_category,
                  });

                  switch (action) {
                    case 'add': {
                      if (!content) {
                        return {
                          success: false,
                          action: 'add',
                          message: 'Content is required for add operation',
                        };
                      }
                      const result = await client.add(content, {
                        user_id: userId,
                        org_id: orgId,
                        project_id: projectId,
                        metadata: {
                          timestamp: new Date().toISOString(),
                          memory_type: 'general',
                        },
                      });
                      if (result.length === 0) {
                        return {
                          success: false,
                          action: 'add',
                          message: 'No memory added',
                        };
                      }
                      // Log sécurisé du résultat
                      log('Memory add result:', {
                        success: true,
                        memoryId: result[0]?.id,
                        timestamp: new Date().toISOString(),
                      });
                      return {
                        success: true,
                        action: 'add',
                        memory: result[0],
                      };
                    }
                    case 'add_personal_info': {
                      if (!content) {
                        return {
                          success: false,
                          action: 'add_personal_info',
                          message:
                            'Content is required for add_personal_info operation',
                        };
                      }

                      // Valider et définir les types et catégories d'informations
                      const validInfoType = info_type || 'preference';
                      const validInfoCategory = info_category || 'general';

                      const result = await client.add(content, {
                        user_id: userId,
                        org_id: orgId,
                        project_id: projectId,
                        metadata: {
                          timestamp: new Date().toISOString(),
                          memory_type: 'personal_info',
                          info_type: validInfoType,
                          info_category: validInfoCategory,
                          // Stocker les catégories dans les métadonnées
                          categories_info: [
                            'personal_info',
                            validInfoType,
                            validInfoCategory,
                          ].join(','),
                        },
                      });

                      if (result.length === 0) {
                        return {
                          success: false,
                          action: 'add_personal_info',
                          message: 'No personal information added',
                        };
                      }

                      log('Personal info add result:', {
                        success: true,
                        memoryId: result[0]?.id,
                        info_type: validInfoType,
                        info_category: validInfoCategory,
                        timestamp: new Date().toISOString(),
                      });

                      return {
                        success: true,
                        action: 'add_personal_info',
                        memory: result[0],
                        info_type: validInfoType,
                        info_category: validInfoCategory,
                      };
                    }
                    case 'search': {
                      if (!query) {
                        return {
                          success: false,
                          action: 'search',
                          message: 'Query is required for search operation',
                        };
                      }

                      try {
                        const result = await client.search(query, {
                          user_id: userId,
                          org_id: orgId,
                          project_id: projectId,
                          api_version: 'v2',
                        });

                        // Log sécurisé des résultats de recherche
                        log('Memory search results:', {
                          success: true,
                          count: Array.isArray(result)
                            ? result.length
                            : result
                              ? 1
                              : 0,
                          timestamp: new Date().toISOString(),
                        });

                        if (!result || result.length === 0) {
                          return {
                            success: true,
                            action: 'search',
                            results: [], // Tableau vide pour indiquer aucun résultat
                          };
                        }

                        // Vérifier la structure du résultat et adapter le traitement
                        const searchResults = Array.isArray(result)
                          ? result
                          : [result];
                        const itemsToProcess = Array.isArray(searchResults[0])
                          ? searchResults[0]
                          : searchResults;

                        // Enrichir les résultats avec des métadonnées plus accessibles
                        const enrichedResults = itemsToProcess.map(
                          (item: any) => ({
                            ...item,
                            // Extraire les métadonnées importantes au premier niveau pour faciliter l'accès
                            chatId: item.metadata?.chatId,
                            messageRole: item.metadata?.role,
                            timestamp: item.metadata?.timestamp,
                            messageId: item.metadata?.messageId,
                            memory_type:
                              item.metadata?.memory_type || 'general',
                            info_type: item.metadata?.info_type,
                            info_category: item.metadata?.info_category,
                          }),
                        );

                        return {
                          success: true,
                          action: 'search',
                          results: enrichedResults,
                        };
                      } catch (error: unknown) {
                        logError('Search error:', error);
                        return {
                          success: false,
                          action: 'search',
                          message: `Error during search: ${error instanceof Error ? error.message : 'Unknown error'}`,
                        };
                      }
                    }
                    case 'search_personal_info': {
                      // Construire une requête spécifique pour les informations personnelles
                      let personalInfoQuery = query || '';

                      // Ajouter des filtres de métadonnées si spécifiés
                      if (info_type || info_category) {
                        // Si une requête existe déjà, l'entourer de parenthèses et ajouter AND
                        if (personalInfoQuery) {
                          personalInfoQuery = `(${personalInfoQuery}) AND `;
                        }

                        // Ajouter les filtres de type et catégorie via les métadonnées
                        if (info_type) {
                          personalInfoQuery += `metadata.info_type:${info_type} `;
                        }

                        if (info_category) {
                          if (info_type) personalInfoQuery += 'AND ';
                          personalInfoQuery += `metadata.info_category:${info_category}`;
                        }
                      }

                      // Si aucune requête n'est spécifiée, rechercher toutes les informations personnelles
                      if (!personalInfoQuery) {
                        personalInfoQuery =
                          'metadata.memory_type:personal_info';
                      }

                      try {
                        // Rechercher avec des filtres spécifiques pour les informations personnelles
                        // Utiliser un dictionnaire pour les filtres comme requis par l'API
                        const result = await client.search(personalInfoQuery, {
                          user_id: userId,
                          org_id: orgId,
                          project_id: projectId,
                          api_version: 'v2',
                          // Pas besoin d'ajouter des filtres de catégories ici, nous utilisons la requête
                        });

                        log('Personal info search results:', {
                          success: true,
                          count: Array.isArray(result)
                            ? result.length
                            : result
                              ? 1
                              : 0,
                          query: personalInfoQuery,
                          info_type,
                          info_category,
                          timestamp: new Date().toISOString(),
                        });

                        if (!result || result.length === 0) {
                          return {
                            success: true,
                            action: 'search_personal_info',
                            results: [],
                          };
                        }

                        // Traiter les résultats
                        const searchResults = Array.isArray(result)
                          ? result
                          : [result];
                        const itemsToProcess = Array.isArray(searchResults[0])
                          ? searchResults[0]
                          : searchResults;

                        // Enrichir les résultats avec des métadonnées plus accessibles
                        const enrichedResults = itemsToProcess.map(
                          (item: any) => ({
                            ...item,
                            memory_type:
                              item.metadata?.memory_type || 'personal_info',
                            info_type: item.metadata?.info_type,
                            info_category: item.metadata?.info_category,
                            timestamp:
                              item.metadata?.timestamp || item.created_at,
                          }),
                        );

                        return {
                          success: true,
                          action: 'search_personal_info',
                          results: enrichedResults,
                        };
                      } catch (error: unknown) {
                        logError('Personal info search error:', error);
                        return {
                          success: false,
                          action: 'search_personal_info',
                          message: `Error during personal info search: ${error instanceof Error ? error.message : 'Unknown error'}`,
                        };
                      }
                    }
                  }
                } catch (error) {
                  logError('Memory operation error:', error);
                  throw error;
                }
              },
            }),
          },
          onFinish: async ({ response }) => {
            if (session.user?.id) {
              try {
                const assistantId = getTrailingMessageId({
                  messages: response.messages.filter(
                    (msg) => msg.role === 'assistant',
                  ),
                });

                if (!assistantId) {
                  throw new Error('No assistant message found!');
                }

                const [, assistantMessage] = appendResponseMessages({
                  messages: [message],
                  responseMessages: response.messages,
                });

                await saveMessages({
                  messages: [
                    {
                      id: assistantId,
                      chatId: id,
                      role: assistantMessage.role,
                      parts: assistantMessage.parts,
                      attachments:
                        assistantMessage.experimental_attachments ?? [],
                      createdAt: new Date(),
                    },
                  ],
                });

                // Sauvegarder les messages dans la mémoire
                if (memoryClient && memoryConfig) {
                  try {
                    // Ne pas sauvegarder à nouveau le message utilisateur s'il a déjà été sauvegardé
                    if (!userMessageSavedToMem0) {
                      log(
                        'Saving user message to memory in onFinish (was not saved earlier)',
                      );
                      await saveMessageToMemory(
                        memoryClient,
                        message,
                        memoryConfig.userId,
                        memoryConfig.orgId,
                        memoryConfig.projectId,
                        id,
                        15000,
                      );
                    }

                    // Toujours sauvegarder le message assistant
                    await saveMessageToMemory(
                      memoryClient,
                      assistantMessage,
                      memoryConfig.userId,
                      memoryConfig.orgId,
                      memoryConfig.projectId,
                      id,
                    );
                  } catch (error) {
                    logError('Failed to save messages to memory:', error);
                  }
                }
              } catch (_) {
                console.error('Failed to save chat');
              }
            }
          },
          experimental_telemetry: {
            isEnabled: isProductionEnvironment,
            functionId: 'stream-text',
          },
        });

        textStream.consumeStream();

        textStream.mergeIntoDataStream(dataStream, {
          sendReasoning: true,
        });
      },
      onError: () => {
        return 'Oops, an error occurred!';
      },
    });
  } catch (error) {
    logError('Request processing error:', error);
    return new Response('An error occurred while processing your request!', {
      status: 500,
    });
  }
}

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const chatId = searchParams.get('chatId');

  if (!chatId) {
    return new Response('Missing chat ID', { status: 400 });
  }

  const session = await auth();

  if (!session?.user?.id) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    // Vérifier si le chat existe
    const chat = await getChatById({ id: chatId });

    if (!chat) {
      return new Response('Chat not found', { status: 404 });
    }

    // Vérifier si l'utilisateur est autorisé à accéder à ce chat
    if (chat.userId !== session.user.id && chat.visibility !== 'public') {
      return new Response('Forbidden', { status: 403 });
    }

    // Créer un flux résumable pour ce chat
    const resumableStream = await createResumableStream({ chatId });

    if (resumableStream) {
      return new Response(resumableStream, { status: 200 });
    } else {
      return new Response('No active stream found for this chat', {
        status: 404,
      });
    }
  } catch (error) {
    console.error('Error resuming chat stream:', error);
    return new Response('An error occurred while processing your request!', {
      status: 500,
    });
  }
}

export async function DELETE(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get('id');

  if (!id) {
    return new Response('Not found', { status: 404 });
  }

  const session = await auth();

  if (!session?.user?.id) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const chat = await getChatById({ id });

    // Vérifier si le chat existe
    if (!chat) {
      return new Response('Chat not found', { status: 404 });
    }

    // Vérifier si l'utilisateur est autorisé à supprimer ce chat
    if (chat.userId !== session.user.id) {
      return new Response('Forbidden', { status: 403 });
    }

    const deletedChat = await deleteChatById({ id });
    return Response.json(deletedChat, { status: 200 });
  } catch (error) {
    console.error('Error deleting chat:', error);
    return new Response('An error occurred while processing your request!', {
      status: 500,
    });
  }
}
