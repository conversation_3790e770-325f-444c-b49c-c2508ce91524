'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import type { Place } from '@/lib/types';
import { CrossIcon, LoaderIcon } from './icons';
import { useIsMobile } from '@/hooks/use-mobile';

interface ImageSidebarProps {
  place: Place | null;
  isOpen: boolean;
  onClose: () => void;
}

interface ImageLoadState {
  [key: string]: 'loading' | 'loaded' | 'error';
}

const ImageSidebar: React.FC<ImageSidebarProps> = ({
  place,
  isOpen,
  onClose,
}) => {
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [imageLoadStates, setImageLoadStates] = useState<ImageLoadState>({});
  const [isClosing, setIsClosing] = useState(false);
  const sidebarRef = useRef<HTMLDivElement>(null);
  const startY = useRef<number>(0);
  const currentY = useRef<number>(0);
  const isDragging = useRef<boolean>(false);

  const isMobile = useIsMobile();

  // Reset selected image when place changes
  useEffect(() => {
    if (place) {
      setSelectedImageIndex(0);
      setImageLoadStates({});
    }
  }, [place?.cid]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleClose = useCallback(() => {
    setIsClosing(true);
    setTimeout(() => {
      onClose();
      setIsClosing(false);
    }, 300);
  }, [onClose]);

  // Touch/swipe handlers for mobile
  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      if (!isMobile) return;
      startY.current = e.touches[0].clientY;
      isDragging.current = true;
    },
    [isMobile],
  );

  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      if (!isDragging.current || !isMobile) return;
      currentY.current = e.touches[0].clientY;
      const deltaY = currentY.current - startY.current;

      if (deltaY > 0 && sidebarRef.current) {
        // Only allow downward swipe
        sidebarRef.current.style.transform = `translateY(${deltaY}px)`;
      }
    },
    [isMobile],
  );

  const handleTouchEnd = useCallback(() => {
    if (!isDragging.current || !isMobile) return;
    const deltaY = currentY.current - startY.current;

    if (sidebarRef.current) {
      if (deltaY > 100) {
        // Swipe down threshold reached - close sidebar
        handleClose();
      } else {
        // Snap back to original position
        sidebarRef.current.style.transform = 'translateY(0)';
      }
    }

    isDragging.current = false;
  }, [isMobile, handleClose]);

  const handleImageLoad = useCallback((imageUrl: string) => {
    setImageLoadStates((prev) => ({
      ...prev,
      [imageUrl]: 'loaded',
    }));
  }, []);

  const handleImageError = useCallback((imageUrl: string) => {
    console.log('Image failed to load:', imageUrl);
    setImageLoadStates((prev) => ({
      ...prev,
      [imageUrl]: 'error',
    }));
  }, []);

  const getImageLoadState = (imageUrl: string) => {
    return imageLoadStates[imageUrl] || 'loading';
  };

  if (!isOpen && !isClosing) return null;

  const images = place?.images || [];
  const hasImages = images.length > 0;

  return (
    <>
      {/* Backdrop */}
      <div
        className={`fixed inset-0 bg-black/50 z-40 transition-opacity duration-300 ${
          isOpen && !isClosing ? 'opacity-100' : 'opacity-0'
        }`}
        onClick={handleClose}
        role="button"
        tabIndex={0}
        aria-label="Fermer la sidebar d'images"
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            handleClose();
          }
        }}
      />

      {/* Image Sidebar */}
      <div
        ref={sidebarRef}
        className={`
          fixed bottom-0 left-0 right-0 z-50
          bg-white dark:bg-gray-900
          rounded-t-2xl shadow-2xl
          max-h-[80vh] sm:max-h-[70vh]
          transition-transform duration-300 ease-out
          ${isOpen && !isClosing ? 'translate-y-0' : 'translate-y-full'}
        `}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* Drag Handle */}
        <div className="flex justify-center pt-2 pb-1">
          <div className="w-12 h-1 bg-gray-300 dark:bg-gray-600 rounded-full" />
        </div>

        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex-1 min-w-0">
            <h3 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white truncate">
              {place?.title || 'Location Images'}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
              {hasImages
                ? `${images.length} image${images.length !== 1 ? 's' : ''}`
                : 'No images available'}
            </p>
          </div>
          <button
            type="button"
            onClick={handleClose}
            className="ml-4 p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
            aria-label="Close image sidebar"
          >
            <CrossIcon />
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          {!hasImages ? (
            <div className="flex flex-col items-center justify-center h-48 text-gray-500 dark:text-gray-400">
              <div className="text-4xl mb-2">📷</div>
              <p className="text-center font-medium">
                No images available from Serper
              </p>
              <p className="text-center text-sm mt-2">
                This location doesn't have images in the search results
              </p>
            </div>
          ) : (
            <div className="h-full flex flex-col">
              {/* Main Image Display */}
              <div className="flex-1 relative bg-gray-100 dark:bg-gray-800">
                {images.map((image, index) => (
                  <div
                    key={`${image.url}-${index}`}
                    className={`absolute inset-0 transition-opacity duration-300 ${
                      index === selectedImageIndex ? 'opacity-100' : 'opacity-0'
                    }`}
                  >
                    {getImageLoadState(image.url) === 'loading' && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <div className="flex items-center space-x-2 text-gray-500">
                          <div className="animate-spin">
                            <LoaderIcon />
                          </div>
                          <span>Loading image...</span>
                        </div>
                      </div>
                    )}

                    {getImageLoadState(image.url) === 'error' ? (
                      <div className="absolute inset-0 flex items-center justify-center text-gray-500">
                        <div className="text-center">
                          <div className="text-4xl mb-2">❌</div>
                          <p>Failed to load image</p>
                        </div>
                      </div>
                    ) : (
                      <img
                        src={image.url}
                        alt={
                          image.description ||
                          `${place?.title} - Image ${index + 1}`
                        }
                        className={`w-full h-full object-contain transition-opacity duration-300 ${
                          getImageLoadState(image.url) === 'loaded'
                            ? 'opacity-100'
                            : 'opacity-0'
                        }`}
                        onLoad={() => handleImageLoad(image.url)}
                        onError={() => handleImageError(image.url)}
                      />
                    )}
                  </div>
                ))}

                {/* Navigation arrows for multiple images */}
                {images.length > 1 && (
                  <>
                    <button
                      type="button"
                      onClick={() =>
                        setSelectedImageIndex((prev) =>
                          prev === 0 ? images.length - 1 : prev - 1,
                        )
                      }
                      className="absolute left-2 top-1/2 -translate-y-1/2 p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors"
                      aria-label="Previous image"
                    >
                      ←
                    </button>
                    <button
                      type="button"
                      onClick={() =>
                        setSelectedImageIndex((prev) =>
                          prev === images.length - 1 ? 0 : prev + 1,
                        )
                      }
                      className="absolute right-2 top-1/2 -translate-y-1/2 p-2 bg-black/50 text-white rounded-full hover:bg-black/70 transition-colors"
                      aria-label="Next image"
                    >
                      →
                    </button>
                  </>
                )}

                {/* Image counter */}
                {images.length > 1 && (
                  <div className="absolute bottom-2 left-1/2 -translate-x-1/2 px-3 py-1 bg-black/50 text-white text-sm rounded-full">
                    {selectedImageIndex + 1} / {images.length}
                  </div>
                )}
              </div>

              {/* Thumbnail strip for multiple images */}
              {images.length > 1 && (
                <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex space-x-2 overflow-x-auto">
                    {images.map((image, index) => (
                      <button
                        type="button"
                        key={`thumb-${image.url}-${index}`}
                        onClick={() => setSelectedImageIndex(index)}
                        className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all ${
                          index === selectedImageIndex
                            ? 'border-blue-500 ring-2 ring-blue-200'
                            : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
                        }`}
                      >
                        <img
                          src={image.thumbnail || image.url}
                          alt={`Thumbnail ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default ImageSidebar;
