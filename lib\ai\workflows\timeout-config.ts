/**
 * Centralized timeout configuration for AI workflows
 * Helps prevent timeout errors and optimize performance
 */

export const TIMEOUT_CONFIG = {
  // API Route timeouts (in seconds)
  API_ROUTES: {
    CHAT: 60,
    ADVANCED_TRIP_PLANNING: 240, // 4 minutes for complex operations with web search (increased)
    TRANSCRIBE: 30,
    ARTIFACTS: 180, // 3 minutes (increased)
  },

  // Workflow operation timeouts (in milliseconds)
  OPERATIONS: {
    DESTINATION_EXTRACTION: 20000, // 20 seconds
    TRIP_CLASSIFICATION: 15000, // 15 seconds
    ORCHESTRATION: 200000, // 200 seconds - main operation (increased for comprehensive web search)
    HTML_GENERATION: 30000, // 30 seconds
    IMAGE_SEARCH: 15000, // 15 seconds
    MEMORY_OPERATIONS: 8000, // 8 seconds
    DATABASE_OPERATIONS: 5000, // 5 seconds
    // Parallel tool operations - generous timeouts for comprehensive results
    PARALLEL_WEB_SEARCH: 40000, // 40 seconds for parallel web searches (comprehensive)
    PARALLEL_MAP_SEARCH: 30000, // 30 seconds for parallel map searches (comprehensive)
    PARALLEL_IMAGE_SEARCH: 25000, // 25 seconds for parallel image searches (comprehensive)
    PARALLEL_AGENT_TOOLS: 50000, // 50 seconds for all tools in an agent (comprehensive)
    // Individual agent timeouts for comprehensive results
    LOGISTICS_AGENT: 30000, // 30 seconds for comprehensive logistics
    ACCOMMODATION_AGENT: 35000, // 35 seconds for comprehensive accommodation
    ACTIVITY_AGENT: 35000, // 35 seconds for comprehensive activities
    LOCAL_EXPERT_AGENT: 35000, // 35 seconds for comprehensive local expert
    POI_AGENT: 30000, // 30 seconds for comprehensive POIs
  },

  // AI Model timeouts (in milliseconds)
  AI_MODELS: {
    FAST_OPERATIONS: 8000, // Quick responses
    STANDARD_OPERATIONS: 25000, // Standard generation
    COMPLEX_OPERATIONS: 45000, // Complex generation
    VERY_COMPLEX_OPERATIONS: 90000, // Very complex operations (increased)
  },

  // Retry configuration
  RETRY: {
    MAX_ATTEMPTS: 3,
    BACKOFF_BASE: 1000, // 1 second
    BACKOFF_MAX: 10000, // 10 seconds
    // Enhanced retry configuration for API overload scenarios
    API_OVERLOAD: {
      MAX_ATTEMPTS: 5, // More attempts for overload scenarios
      BACKOFF_BASE: 2000, // Start with 2 seconds
      BACKOFF_MAX: 30000, // Up to 30 seconds
      JITTER_FACTOR: 0.3, // Add randomness to prevent thundering herd
    },
    // Circuit breaker configuration
    CIRCUIT_BREAKER: {
      FAILURE_THRESHOLD: 5, // Number of consecutive failures before opening circuit
      RECOVERY_TIMEOUT: 60000, // 1 minute before trying again
      HALF_OPEN_MAX_CALLS: 3, // Max calls to test if service is recovered
    },
  },

  // Progress update intervals
  PROGRESS: {
    UPDATE_INTERVAL: 500, // 500ms between updates
    HEARTBEAT_INTERVAL: 5000, // 5 seconds heartbeat
  },
} as const;

/**
 * Get timeout for specific operation
 */
export function getOperationTimeout(
  operation: keyof typeof TIMEOUT_CONFIG.OPERATIONS,
): number {
  return TIMEOUT_CONFIG.OPERATIONS[operation];
}

/**
 * Get API route timeout
 */
export function getApiTimeout(
  route: keyof typeof TIMEOUT_CONFIG.API_ROUTES,
): number {
  return TIMEOUT_CONFIG.API_ROUTES[route];
}

/**
 * Get AI model timeout based on complexity
 */
export function getAiModelTimeout(
  complexity: keyof typeof TIMEOUT_CONFIG.AI_MODELS,
): number {
  return TIMEOUT_CONFIG.AI_MODELS[complexity];
}

/**
 * Create a timeout promise that rejects after specified milliseconds
 */
export function createTimeoutPromise<T>(
  ms: number,
  operation: string,
): Promise<T> {
  return new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error(`Operation "${operation}" timed out after ${ms}ms`));
    }, ms);
  });
}

/**
 * Execute a promise with timeout
 */
export async function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  operation: string,
): Promise<T> {
  return Promise.race([promise, createTimeoutPromise<T>(timeoutMs, operation)]);
}

/**
 * Execute with retry and exponential backoff
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  operationName: string,
  maxAttempts: number = TIMEOUT_CONFIG.RETRY.MAX_ATTEMPTS,
): Promise<T> {
  let lastError: Error = new Error('No attempts made');

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      console.warn(
        `Attempt ${attempt}/${maxAttempts} failed for ${operationName}:`,
        error,
      );

      if (attempt === maxAttempts) {
        break;
      }

      // Exponential backoff
      const delay = Math.min(
        TIMEOUT_CONFIG.RETRY.BACKOFF_BASE * Math.pow(2, attempt - 1),
        TIMEOUT_CONFIG.RETRY.BACKOFF_MAX,
      );

      console.log(`Retrying ${operationName} in ${delay}ms...`);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  throw new Error(
    `Operation "${operationName}" failed after ${maxAttempts} attempts. Last error: ${lastError.message}`,
  );
}

/**
 * Execute with timeout and retry
 */
export async function withTimeoutAndRetry<T>(
  operation: () => Promise<T>,
  timeoutMs: number,
  operationName: string,
  maxAttempts: number = TIMEOUT_CONFIG.RETRY.MAX_ATTEMPTS,
): Promise<T> {
  return withRetry(
    () => withTimeout(operation(), timeoutMs, operationName),
    operationName,
    maxAttempts,
  );
}

/**
 * Check if error is an API overload error (503 status)
 */
function isApiOverloadError(error: any): boolean {
  return (
    error?.statusCode === 503 ||
    error?.status === 503 ||
    error?.message?.includes('overloaded') ||
    error?.message?.includes('503') ||
    error?.message?.includes('UNAVAILABLE')
  );
}

/**
 * Check if error is retryable
 */
function isRetryableError(error: any): boolean {
  // API overload errors are always retryable
  if (isApiOverloadError(error)) return true;

  // Other retryable status codes
  const retryableStatusCodes = [429, 500, 502, 503, 504];
  return retryableStatusCodes.includes(error?.statusCode || error?.status);
}

/**
 * Calculate backoff delay with jitter
 */
function calculateBackoffWithJitter(
  attempt: number,
  baseDelay: number,
  maxDelay: number,
  jitterFactor = 0.3,
): number {
  const exponentialDelay = Math.min(
    baseDelay * Math.pow(2, attempt - 1),
    maxDelay,
  );
  const jitter = exponentialDelay * jitterFactor * Math.random();
  return Math.floor(exponentialDelay + jitter);
}

/**
 * Enhanced retry with smart backoff for API overload scenarios
 */
export async function withEnhancedRetry<T>(
  operation: () => Promise<T>,
  operationName: string,
  options?: {
    maxAttempts?: number;
    baseDelay?: number;
    maxDelay?: number;
    jitterFactor?: number;
  },
): Promise<T> {
  const config = TIMEOUT_CONFIG.RETRY.API_OVERLOAD;
  const maxAttempts = options?.maxAttempts || config.MAX_ATTEMPTS;
  const baseDelay = options?.baseDelay || config.BACKOFF_BASE;
  const maxDelay = options?.maxDelay || config.BACKOFF_MAX;
  const jitterFactor = options?.jitterFactor || config.JITTER_FACTOR;

  let lastError: Error = new Error('No attempts made');

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;

      // Check if error is retryable
      if (!isRetryableError(error)) {
        console.warn(`Non-retryable error for ${operationName}:`, error);
        throw error;
      }

      console.warn(
        `Attempt ${attempt}/${maxAttempts} failed for ${operationName}:`,
        isApiOverloadError(error) ? 'API Overloaded' : error,
      );

      if (attempt === maxAttempts) {
        break;
      }

      // Calculate delay with jitter for API overload scenarios
      const delay = isApiOverloadError(error)
        ? calculateBackoffWithJitter(attempt, baseDelay, maxDelay, jitterFactor)
        : Math.min(
            TIMEOUT_CONFIG.RETRY.BACKOFF_BASE * Math.pow(2, attempt - 1),
            TIMEOUT_CONFIG.RETRY.BACKOFF_MAX,
          );

      console.log(
        `Retrying ${operationName} in ${delay}ms... (${isApiOverloadError(error) ? 'API overload detected' : 'standard retry'})`,
      );
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  throw new Error(
    `Operation "${operationName}" failed after ${maxAttempts} attempts. Last error: ${lastError.message}`,
  );
}

/**
 * Optimized timeout configuration for different environments
 */
export function getEnvironmentTimeouts() {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isTest = process.env.NODE_ENV === 'test';

  if (isTest) {
    // Shorter timeouts for tests
    return {
      ...TIMEOUT_CONFIG,
      OPERATIONS: {
        ...TIMEOUT_CONFIG.OPERATIONS,
        DESTINATION_EXTRACTION: 5000,
        TRIP_CLASSIFICATION: 3000,
        ORCHESTRATION: 15000,
        HTML_GENERATION: 5000,
      },
    };
  }

  if (isDevelopment) {
    // Longer timeouts for development (debugging)
    return {
      ...TIMEOUT_CONFIG,
      OPERATIONS: {
        ...TIMEOUT_CONFIG.OPERATIONS,
        ORCHESTRATION: 120000, // 2 minutes
        HTML_GENERATION: 60000, // 1 minute
      },
    };
  }

  // Production timeouts (default)
  return TIMEOUT_CONFIG;
}

/**
 * Log timeout configuration on startup
 */
export function logTimeoutConfig() {
  const config = getEnvironmentTimeouts();
  console.log('🕐 Timeout Configuration:', {
    environment: process.env.NODE_ENV || 'development',
    apiTimeouts: config.API_ROUTES,
    operationTimeouts: config.OPERATIONS,
    retryConfig: config.RETRY,
  });
}
