import { z } from 'zod';
import { generateObject, type DataStreamWriter } from 'ai';
import type { LanguageModelV1 } from 'ai';
import type { DestinationInfo } from '../types';
import type { UserPreferences } from './preference-agent';
import { TripType, type TripClassification } from './classifier-agent';
import { web_search } from '@/lib/ai/tools/web-search';

/**
 * Interface for cycling route
 */
export interface CyclingRoute {
  name: string;
  description: string;
  distance: string;
  difficulty: 'easy' | 'moderate' | 'challenging' | 'difficult';
  elevationGain: string;
  estimatedTime: string;
  surfaceType: string;
  startPoint: {
    name: string;
    coordinates: {
      lat: string;
      lng: string;
    };
  };
  endPoint: {
    name: string;
    coordinates: {
      lat: string;
      lng: string;
    };
  };
  waypoints: {
    name: string;
    description: string;
    type: 'viewpoint' | 'rest' | 'attraction' | 'food' | 'water' | 'other';
    coordinates: {
      lat: string;
      lng: string;
    };
  }[];
  highlights: string[];
  tips: string[];
  bestTimeToRide: string;
  bikeRentalOptions?: {
    name: string;
    location: string;
    priceRange: string;
    bikeTypes: string[];
    website?: string;
    coordinates: {
      lat: string;
      lng: string;
    };
  }[];
  imageKeywords: string[];
}

/**
 * Interface for hiking trail
 */
export interface HikingTrail {
  name: string;
  description: string;
  distance: string;
  difficulty: 'easy' | 'moderate' | 'challenging' | 'difficult';
  elevationGain: string;
  estimatedTime: string;
  trailType: 'loop' | 'out-and-back' | 'point-to-point' | 'network';
  startPoint: {
    name: string;
    coordinates: {
      lat: string;
      lng: string;
    };
  };
  endPoint?: {
    name: string;
    coordinates: {
      lat: string;
      lng: string;
    };
  };
  waypoints: {
    name: string;
    description: string;
    type: 'viewpoint' | 'rest' | 'attraction' | 'water' | 'other';
    coordinates: {
      lat: string;
      lng: string;
    };
  }[];
  highlights: string[];
  tips: string[];
  bestTimeToHike: string;
  requiredGear: string[];
  imageKeywords: string[];
}

/**
 * Interface for beach activity
 */
export interface BeachActivity {
  beaches: {
    name: string;
    description: string;
    type: 'sandy' | 'rocky' | 'pebble' | 'coral' | 'other';
    facilities: string[];
    activities: string[];
    bestTimeToVisit: string;
    coordinates: {
      lat: string;
      lng: string;
    };
  }[];
  waterActivities: {
    name: string;
    description: string;
    providers?: {
      name: string;
      location: string;
      priceRange: string;
      website?: string;
      coordinates: {
        lat: string;
        lng: string;
      };
    }[];
  }[];
  tips: string[];
  imageKeywords: string[];
}

/**
 * Interface for shopping information
 */
export interface ShoppingInfo {
  areas: {
    name: string;
    description: string;
    type:
      | 'mall'
      | 'street'
      | 'market'
      | 'department-store'
      | 'boutique-district'
      | 'outlet';
    highlights: string[];
    bestFor: string[];
    priceRange: string;
    openingHours: string;
    coordinates: {
      lat: string;
      lng: string;
    };
  }[];
  shops: {
    name: string;
    description: string;
    category: string;
    specialties: string[];
    priceRange: string;
    location: string;
    coordinates: {
      lat: string;
      lng: string;
    };
  }[];
  tips: string[];
  bestTimeToShop: string;
  taxRefundInfo?: string;
  imageKeywords: string[];
}

/**
 * Interface for themed itinerary
 */
export interface ThemedItinerary {
  name: string;
  description: string;
  duration: string;
  highlights: string[];
  stops: {
    name: string;
    description: string;
    timeNeeded: string;
    coordinates: {
      lat: string;
      lng: string;
    };
    tips: string[];
    imageKeywords?: string[];
  }[];
  transportOptions: string[];
  bestTimeToGo: string;
  tips: string[];
}

/**
 * Interface for day trip
 */
export interface DayTrip {
  name: string;
  description: string;
  distance: string;
  transportOptions: string[];
  departurePoint: string;
  highlights: string[];
  itinerary: {
    time: string;
    activity: string;
    description: string;
    location: string;
  }[];
  tips: string[];
  cost: string;
  bookingInfo?: string;
  coordinates: {
    lat: string;
    lng: string;
  };
  imageKeywords?: string[];
}

/**
 * Interface for attraction detail
 */
export interface AttractionDetail {
  name: string;
  description: string;
  category: string;
  location: string;
  coordinates: {
    lat: string;
    lng: string;
  };
  openingHours: {
    days: string;
    hours: string;
    exceptions?: string;
  }[];
  admissionFees: {
    category: string;
    price: string;
  }[];
  timeNeeded: string;
  highlights: string[];
  tips: string[];
  accessibility: string;
  bestTimeToVisit: string;
  nearbyAttractions: {
    name: string;
    distance: string;
    timeToReach: string;
  }[];
  guidedTours?: {
    name: string;
    duration: string;
    price: string;
    languages: string[];
    bookingInfo: string;
  }[];
  imageKeywords?: string[];
}

/**
 * Interface for specialized activity data
 */
export interface SpecializedActivityData {
  cyclingRoutes?: CyclingRoute[];
  hikingTrails?: HikingTrail[];
  beachActivities?: BeachActivity;
  shoppingInfo?: ShoppingInfo;
  themedItineraries?: ThemedItinerary[];
  durationBasedItineraries?: {
    shortStay: ThemedItinerary[];
    mediumStay: ThemedItinerary[];
    longStay: ThemedItinerary[];
  };
  dayTrips?: DayTrip[];
  attractionDetails?: AttractionDetail[];
  guidedTours?: {
    name: string;
    description: string;
    duration: string;
    price: string;
    highlights: string[];
    meetingPoint: string;
    languages: string[];
    bookingInfo: string;
    reviews?: {
      rating: string;
      comment: string;
      source: string;
    }[];
    imageKeywords?: string[];
  }[];
  customSections: {
    title: string;
    content: string;
    type: 'text' | 'list' | 'table' | 'chart';
  }[];
}

/**
 * SpecializedActivityAgent is responsible for providing detailed information
 * about specific activities like cycling, hiking, beach activities, etc.
 */
export class SpecializedActivityAgent {
  private model: LanguageModelV1;

  constructor(model: LanguageModelV1) {
    this.model = model;
  }

  /**
   * Get specialized activity data based on trip classification
   */
  async getSpecializedActivityData(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
    classification: TripClassification,
  ): Promise<SpecializedActivityData> {
    try {
      console.log(
        'Getting specialized activity data for:',
        classification.primaryType,
      );

      // Search for activity information
      const searchResults = await this.searchActivityInfo(
        destinationInfo,
        classification.primaryType,
        classification.primaryActivity,
      );

      // Get specialized data based on trip type and keywords
      const primaryType = classification.primaryType;
      const secondaryTypes = classification.secondaryTypes || [];
      const primaryActivity = classification.primaryActivity || '';
      const keywords = classification.keywords || [];

      // Check for specific activities
      const hasCycling =
        primaryType === TripType.CYCLING ||
        secondaryTypes.includes(TripType.CYCLING) ||
        primaryActivity.toLowerCase().includes('cycling') ||
        primaryActivity.toLowerCase().includes('vélo') ||
        keywords.some(
          (k) =>
            k.toLowerCase().includes('cycling') ||
            k.toLowerCase().includes('vélo') ||
            k.toLowerCase().includes('bike'),
        );

      const hasShopping =
        primaryActivity.toLowerCase().includes('shopping') ||
        primaryActivity.toLowerCase().includes('boutique') ||
        keywords.some(
          (k) =>
            k.toLowerCase().includes('shopping') ||
            k.toLowerCase().includes('boutique') ||
            k.toLowerCase().includes('magasin'),
        );

      // Create result with custom sections
      const result: SpecializedActivityData = {
        customSections: await this.getCustomSections(
          classification.primaryType,
          searchResults,
        ),
      };

      // Add specialized data based on detected activities
      if (hasCycling) {
        console.log('Adding cycling routes to specialized activity data');
        result.cyclingRoutes = await this.getCyclingRoutes(
          destinationInfo,
          userPreferences,
          searchResults,
        );
      }

      if (hasShopping) {
        console.log('Adding shopping information to specialized activity data');
        result.shoppingInfo = await this.getShoppingInfo(
          destinationInfo,
          userPreferences,
          searchResults,
        );
      }

      return result;
    } catch (error) {
      console.error('Error getting specialized activity data:', error);

      // Return empty data if generation fails
      return {
        customSections: [],
      };
    }
  }

  /**
   * Search for activity information
   */
  private async searchActivityInfo(
    destinationInfo: DestinationInfo,
    tripType: TripType,
    primaryActivity: string,
  ): Promise<any> {
    try {
      // Create a mock session and dataStream for the web_search tool
      const mockSession = {
        user: { id: 'system', type: 'guest' as const },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };
      // Create a mock DataStreamWriter
      const mockDataStream: DataStreamWriter = {
        writeMessageAnnotation: () => {},
        write: () => '',
        writeData: () => '',
        writeSource: () => '',
        merge: () => '',
        onError: () => '',
      };

      // Use the web_search tool to get activity information
      const searchTool = web_search({
        session: mockSession,
        dataStream: mockDataStream,
      });

      // Create search queries based on trip type
      const queries = [];

      switch (tripType) {
        case TripType.CYCLING:
          queries.push(
            `best cycling routes in ${destinationInfo.destination} ${destinationInfo.country}`,
            `bike rental in ${destinationInfo.destination}`,
            `cycling tips for ${destinationInfo.destination}`,
            `cycling difficulty ${destinationInfo.destination}`,
          );
          break;

        case TripType.HIKING:
          queries.push(
            `best hiking trails in ${destinationInfo.destination} ${destinationInfo.country}`,
            `hiking difficulty ${destinationInfo.destination}`,
            `hiking tips for ${destinationInfo.destination}`,
            `hiking gear for ${destinationInfo.destination}`,
          );
          break;

        case TripType.BEACH:
          queries.push(
            `best beaches in ${destinationInfo.destination} ${destinationInfo.country}`,
            `water activities in ${destinationInfo.destination}`,
            `beach tips for ${destinationInfo.destination}`,
            `beach facilities in ${destinationInfo.destination}`,
          );
          break;

        default:
          // Use primary activity if no specific trip type
          if (primaryActivity) {
            queries.push(
              `${primaryActivity} in ${destinationInfo.destination} ${destinationInfo.country}`,
              `${primaryActivity} tips for ${destinationInfo.destination}`,
              `best places for ${primaryActivity} in ${destinationInfo.destination}`,
            );
          } else {
            queries.push(
              `activities in ${destinationInfo.destination} ${destinationInfo.country}`,
              `things to do in ${destinationInfo.destination}`,
              `popular activities in ${destinationInfo.destination}`,
            );
          }
      }

      // Execute all search queries in parallel
      const searchPromises = queries.map((query) =>
        // Use type assertion to avoid TypeScript error
        (searchTool.execute as any)({
          queries: [query],
          maxResults: [5],
          topics: ['general'],
        }),
      );

      const results = await Promise.all(searchPromises);
      return results;
    } catch (error) {
      console.error('Error searching activity information:', error);
      return { results: [] };
    }
  }

  /**
   * Get cycling routes
   */
  private async getCyclingRoutes(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
    searchResults: any,
  ): Promise<CyclingRoute[]> {
    try {
      // Generate cycling routes using the model
      const { object: cyclingData } = await generateObject({
        model: this.model,
        system: `You are an expert cycling guide who specializes in creating detailed cycling route information.
        Create detailed cycling routes for the destination based on the search results.
        Include accurate information about distance, difficulty, elevation gain, and estimated time.
        Provide specific start and end points with coordinates, and include waypoints along the route.
        Consider the user's preferences and activity level when recommending routes.`,
        prompt: `Create detailed cycling routes for ${destinationInfo.destination}, ${destinationInfo.country}
        based on these user preferences:

        ${JSON.stringify(userPreferences, null, 2)}

        Use the following search results to inform your recommendations:
        ${JSON.stringify(searchResults, null, 2)}

        Provide 2-3 cycling routes with varying difficulties and distances.
        Include specific details about each route, including:
        - Route name and description
        - Distance and difficulty
        - Elevation gain and estimated time
        - Surface type
        - Start and end points with coordinates
        - Waypoints along the route
        - Highlights and tips
        - Best time to ride
        - Bike rental options if available`,
        schema: z.object({
          routes: z.array(
            z.object({
              name: z.string().describe('Name of the cycling route'),
              description: z.string().describe('Description of the route'),
              distance: z.string().describe('Distance of the route'),
              difficulty: z
                .enum(['easy', 'moderate', 'challenging', 'difficult'])
                .describe('Difficulty level'),
              elevationGain: z.string().describe('Elevation gain of the route'),
              estimatedTime: z
                .string()
                .describe('Estimated time to complete the route'),
              surfaceType: z
                .string()
                .describe('Type of surface (road, gravel, etc.)'),
              startPoint: z.object({
                name: z.string().describe('Name of the start point'),
                coordinates: z.object({
                  lat: z.string().describe('Latitude coordinate'),
                  lng: z.string().describe('Longitude coordinate'),
                }),
              }),
              endPoint: z.object({
                name: z.string().describe('Name of the end point'),
                coordinates: z.object({
                  lat: z.string().describe('Latitude coordinate'),
                  lng: z.string().describe('Longitude coordinate'),
                }),
              }),
              waypoints: z.array(
                z.object({
                  name: z.string().describe('Name of the waypoint'),
                  description: z
                    .string()
                    .describe('Description of the waypoint'),
                  type: z
                    .enum([
                      'viewpoint',
                      'rest',
                      'attraction',
                      'food',
                      'water',
                      'other',
                    ])
                    .describe('Type of waypoint'),
                  coordinates: z.object({
                    lat: z.string().describe('Latitude coordinate'),
                    lng: z.string().describe('Longitude coordinate'),
                  }),
                }),
              ),
              highlights: z
                .array(z.string())
                .describe('Highlights of the route'),
              tips: z.array(z.string()).describe('Tips for the route'),
              bestTimeToRide: z
                .string()
                .describe('Best time to ride this route'),
              bikeRentalOptions: z
                .array(
                  z.object({
                    name: z.string().describe('Name of the bike rental place'),
                    location: z
                      .string()
                      .describe('Location of the bike rental'),
                    priceRange: z
                      .string()
                      .describe('Price range for bike rental'),
                    bikeTypes: z
                      .array(z.string())
                      .describe('Types of bikes available'),
                    website: z
                      .string()
                      .optional()
                      .describe('Website of the bike rental place'),
                    coordinates: z.object({
                      lat: z.string().describe('Latitude coordinate'),
                      lng: z.string().describe('Longitude coordinate'),
                    }),
                  }),
                )
                .optional()
                .describe('Bike rental options'),
              imageKeywords: z
                .array(z.string())
                .describe('Keywords for images of this route'),
            }),
          ),
        }),
        temperature: 0.7,
      });

      return cyclingData.routes;
    } catch (error) {
      console.error('Error getting cycling routes:', error);
      return [];
    }
  }

  /**
   * Get hiking trails
   */
  private async getHikingTrails(
    _destinationInfo: DestinationInfo,
    _userPreferences: UserPreferences,
    _searchResults: any,
  ): Promise<HikingTrail[]> {
    // Implementation similar to getCyclingRoutes but for hiking trails
    return [];
  }

  /**
   * Get beach activities
   */
  private async getBeachActivities(
    _destinationInfo: DestinationInfo,
    _userPreferences: UserPreferences,
    _searchResults: any,
  ): Promise<BeachActivity> {
    // Implementation similar to getCyclingRoutes but for beach activities
    return {
      beaches: [],
      waterActivities: [],
      tips: [],
      imageKeywords: [],
    };
  }

  /**
   * Get shopping information
   */
  private async getShoppingInfo(
    destinationInfo: DestinationInfo,
    userPreferences: UserPreferences,
    searchResults: any,
  ): Promise<ShoppingInfo> {
    try {
      // Create search queries specifically for shopping
      const shoppingQueries = [
        `best shopping areas in ${destinationInfo.destination} ${destinationInfo.country}`,
        `shopping malls in ${destinationInfo.destination}`,
        `boutiques in ${destinationInfo.destination}`,
        `shopping streets in ${destinationInfo.destination}`,
        `markets in ${destinationInfo.destination}`,
        `shopping tips for ${destinationInfo.destination}`,
      ];

      // Create a mock session and dataStream for the web_search tool
      const mockSession = {
        user: { id: 'system', type: 'guest' as const },
        expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      };
      // Create a mock DataStreamWriter
      const mockDataStream: DataStreamWriter = {
        writeMessageAnnotation: () => {},
        write: () => '',
        writeData: () => '',
        writeSource: () => '',
        merge: () => '',
        onError: () => '',
      };

      // Use the web_search tool to get shopping information
      const searchTool = web_search({
        session: mockSession,
        dataStream: mockDataStream,
      });

      // Execute all search queries in parallel
      const shoppingSearchPromises = shoppingQueries.map((query) =>
        // Use type assertion to avoid TypeScript error
        (searchTool.execute as any)({
          queries: [query],
          maxResults: [5],
          topics: ['general'],
        }),
      );

      const shoppingResults = await Promise.all(shoppingSearchPromises);

      // Combine with existing search results
      const combinedResults = [...searchResults, ...shoppingResults];

      // Generate shopping information using the model
      const { object: shoppingData } = await generateObject({
        model: this.model,
        system: `You are an expert shopping guide who specializes in creating detailed shopping information.
        Create detailed shopping information for the destination based on the search results.
        Include accurate information about shopping areas, malls, boutiques, markets, and department stores.
        Provide specific locations with coordinates, and include highlights and tips.
        Consider the user's preferences and budget when recommending shopping options.`,
        prompt: `Create detailed shopping information for ${destinationInfo.destination}, ${destinationInfo.country}
        based on these user preferences:

        ${JSON.stringify(userPreferences, null, 2)}

        Use the following search results to inform your recommendations:
        ${JSON.stringify(combinedResults, null, 2)}

        Include specific details about:
        - Shopping areas (malls, streets, markets, etc.)
        - Notable shops and boutiques
        - Price ranges and specialties
        - Best times to shop
        - Shopping tips
        - Tax refund information if applicable`,
        schema: z.object({
          areas: z.array(
            z.object({
              name: z.string().describe('Name of the shopping area'),
              description: z.string().describe('Description of the area'),
              type: z
                .enum([
                  'mall',
                  'street',
                  'market',
                  'department-store',
                  'boutique-district',
                  'outlet',
                ])
                .describe('Type of shopping area'),
              highlights: z
                .array(z.string())
                .describe('Highlights of the area'),
              bestFor: z
                .array(z.string())
                .describe('What this area is best for'),
              priceRange: z.string().describe('Price range of the area'),
              openingHours: z.string().describe('Opening hours information'),
              coordinates: z.object({
                lat: z.string().describe('Latitude coordinate'),
                lng: z.string().describe('Longitude coordinate'),
              }),
            }),
          ),
          shops: z.array(
            z.object({
              name: z.string().describe('Name of the shop'),
              description: z.string().describe('Description of the shop'),
              category: z.string().describe('Category of the shop'),
              specialties: z
                .array(z.string())
                .describe('Specialties of the shop'),
              priceRange: z.string().describe('Price range of the shop'),
              location: z.string().describe('Location description'),
              coordinates: z.object({
                lat: z.string().describe('Latitude coordinate'),
                lng: z.string().describe('Longitude coordinate'),
              }),
            }),
          ),
          tips: z.array(z.string()).describe('Shopping tips'),
          bestTimeToShop: z.string().describe('Best time to shop'),
          taxRefundInfo: z
            .string()
            .optional()
            .describe('Tax refund information if applicable'),
          imageKeywords: z
            .array(z.string())
            .describe('Keywords for images related to shopping'),
        }),
        temperature: 0.7,
      });

      return shoppingData;
    } catch (error) {
      console.error('Error getting shopping information:', error);

      // Return basic shopping info if generation fails
      return {
        areas: [
          {
            name: `Main Shopping Area in ${destinationInfo.destination}`,
            description:
              'The main shopping area with various shops and boutiques.',
            type: 'street',
            highlights: ['Various shops', 'Local boutiques'],
            bestFor: ['General shopping', 'Souvenirs'],
            priceRange: 'Moderate',
            openingHours: 'Typically 10:00-19:00, may vary by shop',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
          },
        ],
        shops: [
          {
            name: 'Local Boutique',
            description: 'A local boutique offering unique items.',
            category: 'Fashion',
            specialties: ['Local designs', 'Unique items'],
            priceRange: 'Moderate to High',
            location: 'City Center',
            coordinates: {
              lat: destinationInfo.coordinates.lat,
              lng: destinationInfo.coordinates.lng,
            },
          },
        ],
        tips: [
          'Most shops are closed on Sundays',
          'Bargaining is not common in department stores but may be acceptable in markets',
          'Look for tax-free shopping options if you are a tourist',
        ],
        bestTimeToShop: 'Weekday mornings tend to be less crowded',
        imageKeywords: [
          'shopping',
          destinationInfo.destination,
          'boutique',
          'market',
        ],
      };
    }
  }

  /**
   * Get custom sections based on trip type
   */
  private async getCustomSections(
    tripType: TripType,
    searchResults: any,
  ): Promise<
    {
      title: string;
      content: string;
      type: 'text' | 'list' | 'table' | 'chart';
    }[]
  > {
    try {
      // Generate custom sections based on trip type
      const customSections: {
        title: string;
        content: string;
        type: 'text' | 'list' | 'table' | 'chart';
      }[] = [];

      switch (tripType) {
        case TripType.CYCLING:
          customSections.push(
            {
              title: 'Cycling Season',
              content:
                'Information about the best cycling season in the destination.',
              type: 'text',
            },
            {
              title: 'Local Cycling Events',
              content:
                'Information about local cycling events in the destination.',
              type: 'list',
            },
            {
              title: 'Cycling Gear Checklist',
              content: 'Checklist of cycling gear to bring for the trip.',
              type: 'list',
            },
          );
          break;

        case TripType.CITY:
          if (
            searchResults.some((r: any) =>
              r.toString().toLowerCase().includes('shopping'),
            )
          ) {
            customSections.push(
              {
                title: 'Shopping Districts',
                content:
                  'Information about the main shopping districts in the destination.',
                type: 'list',
              },
              {
                title: 'Shopping Hours',
                content:
                  'Information about typical shopping hours in the destination.',
                type: 'text',
              },
              {
                title: 'Tax Refund Information',
                content:
                  'Information about tax refunds for tourists in the destination.',
                type: 'text',
              },
            );
          }
          break;

        default:
          // No custom sections for other trip types
          break;
      }

      return customSections;
    } catch (error) {
      console.error('Error getting custom sections:', error);
      return [];
    }
  }
}
